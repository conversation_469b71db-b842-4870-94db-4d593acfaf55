// Theme Manager for YouTube Player App
// Following YouTube's theme switching patterns with enhanced device detection

class ThemeManager {
    constructor() {
        this.currentTheme = 'auto';
        this.systemTheme = 'dark';
        this.observers = new Set();
        this.isTransitioning = false;
        this.transitionDuration = 300; // ms
        
        // Available themes
        this.themes = {
            light: {
                name: 'Light',
                description: 'Light theme with bright colors',
                icon: 'fas fa-sun'
            },
            dark: {
                name: 'Dark', 
                description: 'Dark theme with muted colors',
                icon: 'fas fa-moon'
            },
            auto: {
                name: 'Auto (System)',
                description: 'Follows your system preference',
                icon: 'fas fa-adjust'
            }
        };

        // Initialize theme detection
        this.initializeThemeDetection();
        this.detectSystemTheme();
        this.setupMediaQueryListener();
    }

    // Initialize theme detection following YouTube's approach
    initializeThemeDetection() {
        try {
            // Check if browser supports prefers-color-scheme
            if (window.matchMedia) {
                this.supportsSystemTheme = true;
                console.log('🎨 System theme detection supported');
            } else {
                this.supportsSystemTheme = false;
                console.warn('🎨 System theme detection not supported');
            }
        } catch (error) {
            console.error('Error initializing theme detection:', error);
            this.supportsSystemTheme = false;
        }
    }

    // Detect current system theme
    detectSystemTheme() {
        if (!this.supportsSystemTheme) {
            this.systemTheme = 'dark'; // Default fallback
            return;
        }

        try {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            this.systemTheme = darkModeQuery.matches ? 'dark' : 'light';
            
            console.log(`🎨 System theme detected: ${this.systemTheme}`);
            
            // Notify observers about system theme detection
            this.notifyObservers('systemThemeDetected', {
                theme: this.systemTheme,
                supported: this.supportsSystemTheme
            });
        } catch (error) {
            console.error('Error detecting system theme:', error);
            this.systemTheme = 'dark';
        }
    }

    // Setup media query listener for system theme changes
    setupMediaQueryListener() {
        if (!this.supportsSystemTheme) return;

        try {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            // Modern browsers
            if (darkModeQuery.addEventListener) {
                darkModeQuery.addEventListener('change', (e) => {
                    this.handleSystemThemeChange(e.matches ? 'dark' : 'light');
                });
            } 
            // Fallback for older browsers
            else if (darkModeQuery.addListener) {
                darkModeQuery.addListener((e) => {
                    this.handleSystemThemeChange(e.matches ? 'dark' : 'light');
                });
            }
        } catch (error) {
            console.error('Error setting up media query listener:', error);
        }
    }

    // Handle system theme changes
    handleSystemThemeChange(newSystemTheme) {
        const oldSystemTheme = this.systemTheme;
        this.systemTheme = newSystemTheme;
        
        console.log(`🎨 System theme changed from ${oldSystemTheme} to ${newSystemTheme}`);
        
        // If current theme is auto, apply the new system theme
        if (this.currentTheme === 'auto') {
            this.applyTheme('auto', false); // Don't save, just apply
        }
        
        // Notify observers
        this.notifyObservers('systemThemeChanged', {
            oldTheme: oldSystemTheme,
            newTheme: newSystemTheme,
            autoThemeActive: this.currentTheme === 'auto'
        });
    }

    // Get effective theme (resolves 'auto' to actual theme)
    getEffectiveTheme() {
        if (this.currentTheme === 'auto') {
            return this.systemTheme;
        }
        return this.currentTheme;
    }

    // Apply theme with smooth transitions
    async applyTheme(theme, save = true) {
        if (this.isTransitioning) {
            console.log('🎨 Theme transition already in progress, skipping');
            return false;
        }

        const oldTheme = this.currentTheme;
        const oldEffectiveTheme = this.getEffectiveTheme();
        
        this.currentTheme = theme;
        const newEffectiveTheme = this.getEffectiveTheme();
        
        // Skip if effective theme hasn't changed
        if (oldEffectiveTheme === newEffectiveTheme && oldTheme === theme) {
            return true;
        }

        this.isTransitioning = true;
        
        try {
            // Notify observers about theme change start
            this.notifyObservers('themeChangeStart', {
                oldTheme,
                newTheme: theme,
                oldEffectiveTheme,
                newEffectiveTheme
            });

            // Apply theme to DOM with smooth transition
            await this.applyThemeToDOM(newEffectiveTheme);
            
            // Update theme toggle icon
            this.updateThemeIcon();
            
            // Save theme preference if requested
            if (save) {
                await this.saveThemePreference(theme);
            }
            
            console.log(`🎨 Theme applied: ${oldTheme} -> ${theme} (effective: ${newEffectiveTheme})`);
            
            // Notify observers about successful theme change
            this.notifyObservers('themeChanged', {
                oldTheme,
                newTheme: theme,
                effectiveTheme: newEffectiveTheme,
                saved: save
            });
            
            return true;
        } catch (error) {
            console.error('Error applying theme:', error);
            
            // Notify observers about theme change error
            this.notifyObservers('themeChangeError', {
                error,
                attemptedTheme: theme,
                currentTheme: oldTheme
            });
            
            // Revert to old theme
            this.currentTheme = oldTheme;
            return false;
        } finally {
            // Reset transition flag after delay
            setTimeout(() => {
                this.isTransitioning = false;
            }, this.transitionDuration);
        }
    }

    // Apply theme to DOM elements
    async applyThemeToDOM(effectiveTheme) {
        return new Promise((resolve) => {
            // Add transition class for smooth animation
            document.body.classList.add('theme-transitioning');
            
            // Apply theme attribute
            document.body.setAttribute('data-theme', effectiveTheme);
            
            // Force reflow to ensure transition is applied
            document.body.offsetHeight;
            
            // Remove transition class after animation
            setTimeout(() => {
                document.body.classList.remove('theme-transitioning');
                resolve();
            }, this.transitionDuration);
        });
    }

    // Update theme toggle icon based on current theme
    updateThemeIcon() {
        const themeToggleBtn = document.getElementById('themeToggleBtn');
        if (!themeToggleBtn) return;

        const icon = themeToggleBtn.querySelector('i');
        if (!icon) return;

        const themeInfo = this.themes[this.currentTheme];
        if (themeInfo) {
            icon.className = themeInfo.icon;
            themeToggleBtn.title = `Switch to ${this.getNextTheme().name}`;
        }
    }

    // Get next theme in cycle
    getNextTheme() {
        const themeOrder = ['dark', 'light', 'auto'];
        const currentIndex = themeOrder.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeOrder.length;
        const nextThemeKey = themeOrder[nextIndex];
        return { key: nextThemeKey, ...this.themes[nextThemeKey] };
    }

    // Toggle theme (cycle through themes)
    async toggleTheme() {
        const nextTheme = this.getNextTheme();
        return await this.applyTheme(nextTheme.key);
    }

    // Save theme preference
    async saveThemePreference(theme) {
        try {
            if (window.electronAPI && window.electronAPI.saveSettings) {
                const currentSettings = await window.electronAPI.getSettings();
                await window.electronAPI.saveSettings({
                    ...currentSettings,
                    theme: theme
                });
            } else {
                // Fallback to localStorage
                localStorage.setItem('youtube-player-theme', theme);
            }
        } catch (error) {
            console.error('Error saving theme preference:', error);
            throw error;
        }
    }

    // Load theme preference
    async loadThemePreference() {
        try {
            let savedTheme = 'auto'; // Default

            if (window.electronAPI && window.electronAPI.getSettings) {
                const settings = await window.electronAPI.getSettings();
                savedTheme = settings.theme || 'auto';
            } else {
                // Fallback to localStorage
                savedTheme = localStorage.getItem('youtube-player-theme') || 'auto';
            }

            await this.applyTheme(savedTheme, false); // Don't save again
            return savedTheme;
        } catch (error) {
            console.error('Error loading theme preference:', error);
            await this.applyTheme('auto', false); // Fallback to auto
            return 'auto';
        }
    }

    // Get available themes
    getAvailableThemes() {
        return Object.entries(this.themes).map(([key, theme]) => ({
            key,
            ...theme,
            isCurrent: key === this.currentTheme,
            isEffective: key === this.getEffectiveTheme()
        }));
    }

    // Add observer for theme changes
    addObserver(callback) {
        this.observers.add(callback);
    }

    // Remove observer
    removeObserver(callback) {
        this.observers.delete(callback);
    }

    // Notify all observers
    notifyObservers(event, data) {
        this.observers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in theme observer:', error);
            }
        });
    }

    // Get current theme info
    getCurrentTheme() {
        return {
            current: this.currentTheme,
            effective: this.getEffectiveTheme(),
            system: this.systemTheme,
            isTransitioning: this.isTransitioning,
            supportsSystemTheme: this.supportsSystemTheme
        };
    }
}

// Export the ThemeManager class
export default ThemeManager;
