/**
 * YouTube-Style Theme Manager
 * Comprehensive theme management system with app settings integration
 */

class ThemeManager {
  constructor(options = {}) {
    this.options = {
      storageKey: 'youtube-app-theme',
      defaultTheme: 'auto',
      respectSystemPreference: true,
      appSettingsIntegration: null,
      onThemeChange: null,
      ...options
    };

    this.currentTheme = this.options.defaultTheme;
    this.systemTheme = this.getSystemTheme();
    this.listeners = new Set();
    this.isInitialized = false;
    
    // Bind methods
    this.handleSystemThemeChange = this.handleSystemThemeChange.bind(this);
    this.handleStorageChange = this.handleStorageChange.bind(this);
    
    this.init();
  }

  /**
   * Initialize the theme manager
   */
  init() {
    try {
      // Load saved theme or detect system preference
      this.currentTheme = this.loadTheme();
      
      // Apply initial theme
      this.applyTheme(this.currentTheme, { skipSave: true, skipNotify: true });
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Integrate with app settings if available
      this.setupAppSettingsIntegration();
      
      this.isInitialized = true;
      
      // Notify initial theme
      this.notifyThemeChange(this.currentTheme, null);
      
      console.log(`ThemeManager initialized with theme: ${this.currentTheme}`);
    } catch (error) {
      console.error('ThemeManager initialization failed:', error);
      this.fallbackToSystemTheme();
    }
  }

  /**
   * Set the current theme
   */
  setTheme(theme, options = {}) {
    const { skipSave = false, skipNotify = false, skipAppSync = false } = options;
    
    if (!this.isValidTheme(theme)) {
      console.warn(`Invalid theme: ${theme}. Using fallback.`);
      theme = this.options.defaultTheme;
    }

    const previousTheme = this.currentTheme;
    this.currentTheme = theme;

    try {
      // Apply theme to DOM
      this.applyTheme(theme, { skipSave, skipNotify });
      
      // Save to storage
      if (!skipSave) {
        this.saveTheme(theme);
      }
      
      // Sync with app settings
      if (!skipAppSync && this.options.appSettingsIntegration) {
        this.syncToAppSettings(theme);
      }
      
      // Notify listeners
      if (!skipNotify) {
        this.notifyThemeChange(theme, previousTheme);
      }
      
      return true;
    } catch (error) {
      console.error('Failed to set theme:', error);
      this.currentTheme = previousTheme;
      return false;
    }
  }

  /**
   * Get the current theme
   */
  getTheme() {
    return this.currentTheme;
  }

  /**
   * Get the effective theme (resolves 'auto' to actual theme)
   */
  getEffectiveTheme() {
    if (this.currentTheme === 'auto') {
      return this.systemTheme;
    }
    return this.currentTheme;
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme() {
    const effectiveTheme = this.getEffectiveTheme();
    const newTheme = effectiveTheme === 'dark' ? 'light' : 'dark';
    return this.setTheme(newTheme);
  }

  /**
   * Apply theme to DOM
   */
  applyTheme(theme, options = {}) {
    const { skipNotify = false } = options;
    
    try {
      // Remove existing theme attributes
      document.documentElement.removeAttribute('data-theme');
      document.body.removeAttribute('data-theme');
      
      // Apply new theme
      if (theme !== 'auto') {
        document.documentElement.setAttribute('data-theme', theme);
        document.body.setAttribute('data-theme', theme);
      }
      
      // Update meta theme-color for mobile browsers
      this.updateMetaThemeColor(theme);
      
      // Update theme toggle button icon
      this.updateThemeToggleIcon(theme);
      
      // Dispatch custom event
      if (!skipNotify) {
        const event = new CustomEvent('themechange', {
          detail: { theme, effectiveTheme: this.getEffectiveTheme() }
        });
        document.dispatchEvent(event);
      }
      
    } catch (error) {
      console.error('Failed to apply theme:', error);
      throw error;
    }
  }

  /**
   * Update theme toggle button icon
   */
  updateThemeToggleIcon(theme) {
    try {
      const themeToggleBtn = document.getElementById('themeToggleBtn');
      if (themeToggleBtn) {
        const icon = themeToggleBtn.querySelector('i');
        if (icon) {
          const effectiveTheme = theme === 'auto' ? this.systemTheme : theme;
          icon.className = effectiveTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
          themeToggleBtn.title = effectiveTheme === 'dark' ? 'Switch to Light Theme' : 'Switch to Dark Theme';
        }
      }
    } catch (error) {
      console.error('Failed to update theme toggle icon:', error);
    }
  }

  /**
   * Load theme from storage
   */
  loadTheme() {
    try {
      // Try localStorage first
      const stored = localStorage.getItem(this.options.storageKey);
      if (stored && this.isValidTheme(stored)) {
        return stored;
      }
      
      // Try app settings if available
      if (this.options.appSettingsIntegration) {
        const appTheme = this.options.appSettingsIntegration.getTheme?.();
        if (appTheme && this.isValidTheme(appTheme)) {
          return appTheme;
        }
      }
      
      // Fall back to system preference or default
      return this.options.respectSystemPreference ? 'auto' : this.options.defaultTheme;
      
    } catch (error) {
      console.error('Failed to load theme:', error);
      return this.options.defaultTheme;
    }
  }

  /**
   * Save theme to storage
   */
  saveTheme(theme) {
    try {
      localStorage.setItem(this.options.storageKey, theme);
      
      // Also save via IPC if available
      if (window.electronAPI && window.electronAPI.saveTheme) {
        window.electronAPI.saveTheme(theme);
      }
    } catch (error) {
      console.error('Failed to save theme:', error);
    }
  }

  /**
   * Get system theme preference
   */
  getSystemTheme() {
    try {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    } catch (error) {
      console.error('Failed to detect system theme:', error);
      return 'light';
    }
  }

  /**
   * Check if theme is valid
   */
  isValidTheme(theme) {
    return ['light', 'dark', 'auto'].includes(theme);
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    try {
      // System theme change
      if (this.options.respectSystemPreference) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', this.handleSystemThemeChange);
      }
      
      // Storage changes (sync across tabs)
      window.addEventListener('storage', this.handleStorageChange);
      
      // IPC theme changes (from main process)
      if (window.electronAPI && window.electronAPI.onThemeChange) {
        window.electronAPI.onThemeChange((theme) => {
          if (theme !== this.currentTheme) {
            this.setTheme(theme, { skipSave: true });
          }
        });
      }
      
    } catch (error) {
      console.error('Failed to setup event listeners:', error);
    }
  }

  /**
   * Handle system theme change
   */
  handleSystemThemeChange(event) {
    this.systemTheme = event.matches ? 'dark' : 'light';
    
    // Only update if current theme is 'auto'
    if (this.currentTheme === 'auto') {
      this.applyTheme('auto');
      this.notifyThemeChange('auto', 'auto');
    }
  }

  /**
   * Handle storage change (cross-tab sync)
   */
  handleStorageChange(event) {
    if (event.key === this.options.storageKey && event.newValue) {
      const newTheme = event.newValue;
      if (this.isValidTheme(newTheme) && newTheme !== this.currentTheme) {
        this.setTheme(newTheme, { skipSave: true });
      }
    }
  }

  /**
   * Set up app settings integration
   */
  setupAppSettingsIntegration() {
    if (!this.options.appSettingsIntegration) return;
    
    try {
      const appSettings = this.options.appSettingsIntegration;
      
      // Listen for app settings changes
      if (typeof appSettings.onThemeChange === 'function') {
        appSettings.onThemeChange((theme) => {
          if (theme !== this.currentTheme) {
            this.setTheme(theme, { skipAppSync: true });
          }
        });
      }
      
    } catch (error) {
      console.error('Failed to setup app settings integration:', error);
    }
  }

  /**
   * Sync theme to app settings
   */
  syncToAppSettings(theme) {
    try {
      if (this.options.appSettingsIntegration?.setTheme) {
        this.options.appSettingsIntegration.setTheme(theme);
      }
    } catch (error) {
      console.error('Failed to sync theme to app settings:', error);
    }
  }

  /**
   * Update meta theme-color for mobile browsers
   */
  updateMetaThemeColor(theme) {
    try {
      const effectiveTheme = theme === 'auto' ? this.systemTheme : theme;
      const color = effectiveTheme === 'dark' ? '#0f0f0f' : '#ffffff';
      
      let metaTag = document.querySelector('meta[name="theme-color"]');
      if (!metaTag) {
        metaTag = document.createElement('meta');
        metaTag.name = 'theme-color';
        document.head.appendChild(metaTag);
      }
      metaTag.content = color;
      
    } catch (error) {
      console.error('Failed to update meta theme-color:', error);
    }
  }

  /**
   * Add theme change listener
   */
  addListener(callback) {
    if (typeof callback === 'function') {
      this.listeners.add(callback);
    }
  }

  /**
   * Remove theme change listener
   */
  removeListener(callback) {
    this.listeners.delete(callback);
  }

  /**
   * Notify all listeners of theme change
   */
  notifyThemeChange(newTheme, previousTheme) {
    try {
      const effectiveTheme = this.getEffectiveTheme();
      
      // Call option callback
      if (this.options.onThemeChange) {
        this.options.onThemeChange(newTheme, previousTheme, effectiveTheme);
      }
      
      // Call registered listeners
      this.listeners.forEach(callback => {
        try {
          callback(newTheme, previousTheme, effectiveTheme);
        } catch (error) {
          console.error('Theme listener error:', error);
        }
      });
      
    } catch (error) {
      console.error('Failed to notify theme change:', error);
    }
  }

  /**
   * Fallback to system theme on errors
   */
  fallbackToSystemTheme() {
    try {
      this.currentTheme = 'auto';
      this.applyTheme('auto', { skipSave: true });
      console.warn('ThemeManager fell back to system theme');

      // Show user notification if possible
      if (typeof window !== 'undefined' && window.showNotification) {
        window.showNotification('Theme system recovered using system preference', 'warning');
      }
    } catch (error) {
      console.error('Fallback failed:', error);

      // Last resort: apply basic theme manually
      try {
        document.documentElement.removeAttribute('data-theme');
        document.body.style.backgroundColor = this.systemTheme === 'dark' ? '#0f0f0f' : '#ffffff';
        document.body.style.color = this.systemTheme === 'dark' ? '#ffffff' : '#000000';
      } catch (finalError) {
        console.error('Final fallback failed:', finalError);
      }
    }
  }

  /**
   * Validate theme system integrity
   */
  validateThemeSystem() {
    const issues = [];

    // Check CSS variables support
    if (!CSS.supports('color', 'var(--test)')) {
      issues.push('CSS variables not supported');
    }

    // Check localStorage availability
    try {
      localStorage.setItem('theme-test', 'test');
      localStorage.removeItem('theme-test');
    } catch (error) {
      issues.push('localStorage not available');
    }

    // Check DOM manipulation capabilities
    try {
      const testEl = document.createElement('div');
      testEl.setAttribute('data-theme', 'test');
      if (testEl.getAttribute('data-theme') !== 'test') {
        issues.push('DOM manipulation limited');
      }
    } catch (error) {
      issues.push('DOM manipulation failed');
    }

    return {
      isValid: issues.length === 0,
      issues: issues
    };
  }

  /**
   * Repair theme system if possible
   */
  repairThemeSystem() {
    try {
      const validation = this.validateThemeSystem();

      if (!validation.isValid) {
        console.warn('Theme system issues detected:', validation.issues);

        // Attempt repairs
        validation.issues.forEach(issue => {
          switch (issue) {
            case 'CSS variables not supported':
              this.enableCSSVariablesFallback();
              break;
            case 'localStorage not available':
              this.enableMemoryStorage();
              break;
            case 'DOM manipulation limited':
              this.enableLimitedDOMMode();
              break;
          }
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Theme system repair failed:', error);
      return false;
    }
  }

  /**
   * Enable CSS variables fallback for older browsers
   */
  enableCSSVariablesFallback() {
    console.log('Enabling CSS variables fallback');

    // Create style element with hardcoded theme styles
    const style = document.createElement('style');
    style.id = 'theme-fallback-styles';
    style.textContent = `
      body { transition: background-color 0.3s, color 0.3s; }
      [data-theme="dark"] { background-color: #0f0f0f; color: #ffffff; }
      [data-theme="light"] { background-color: #ffffff; color: #000000; }
    `;
    document.head.appendChild(style);
  }

  /**
   * Enable memory storage fallback
   */
  enableMemoryStorage() {
    console.log('Enabling memory storage fallback');

    // Create in-memory storage
    this.memoryStorage = new Map();

    // Override storage methods
    this.saveTheme = (theme) => {
      this.memoryStorage.set(this.options.storageKey, theme);
    };

    this.loadTheme = () => {
      return this.memoryStorage.get(this.options.storageKey) || this.options.defaultTheme;
    };
  }

  /**
   * Enable limited DOM mode
   */
  enableLimitedDOMMode() {
    console.log('Enabling limited DOM mode');

    // Use only basic DOM operations
    this.applyTheme = (theme, options = {}) => {
      try {
        // Apply basic styles directly
        const effectiveTheme = theme === 'auto' ? this.systemTheme : theme;

        if (effectiveTheme === 'dark') {
          document.body.style.backgroundColor = '#0f0f0f';
          document.body.style.color = '#ffffff';
        } else {
          document.body.style.backgroundColor = '#ffffff';
          document.body.style.color = '#000000';
        }

        if (!options.skipNotify) {
          this.notifyThemeChange(theme, this.currentTheme);
        }
      } catch (error) {
        console.error('Limited DOM theme application failed:', error);
      }
    };
  }

  /**
   * Destroy the theme manager
   */
  destroy() {
    try {
      // Remove event listeners
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.removeEventListener('change', this.handleSystemThemeChange);
      window.removeEventListener('storage', this.handleStorageChange);
      
      // Clear listeners
      this.listeners.clear();
      
      this.isInitialized = false;
      
    } catch (error) {
      console.error('Failed to destroy ThemeManager:', error);
    }
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeManager;
} else if (typeof window !== 'undefined') {
  window.ThemeManager = ThemeManager;
}
