// YouTube Desktop Player Renderer Process
// Comprehensive integration with robust theme and language synchronization

// Enhanced systems will be loaded dynamically to avoid module conflicts
let i18n = null;
let ThemeManager = null;

class YouTubePlayer {
    constructor() {
        this.currentSettings = {};
        this.currentQuality = 'auto';
        this.isSettingsOpen = false;
        this.isQualityOverlayOpen = false;

        // Enhanced language and theme management
        this.i18n = null;
        this.themeManager = null;
        this.isApplyingLanguage = false;
        this.lastAppliedLanguage = null;
        this.isApplyingTheme = false;
        this.enhancedSystemsLoaded = false;

        // Loading states
        this.loadingStates = {
            settings: false,
            language: false,
            theme: false,
            quality: false
        };

        // Legacy translation system (will be replaced by i18n)
        this.translations = {
            en: {
                // Header buttons
                'Go Back': 'Go Back',
                'Go Forward': 'Go Forward',
                'Reload': 'Reload',
                'Toggle Theme': 'Toggle Theme',
                'Settings': 'Settings',
                'Toggle Fullscreen': 'Toggle Fullscreen',
                'Enter YouTube URL or search...': 'Enter YouTube URL or search...',

                // Settings modal
                'Video Quality': 'Video Quality',
                'Player Settings': 'Player Settings',
                'Appearance': 'Appearance',
                'Auto': 'Auto',
                'Autoplay': 'Autoplay',
                'Default Volume': 'Default Volume',
                'Theme': 'Theme',
                'Language': 'Language',
                'Dark': 'Dark',
                'Light': 'Light',
                'Save Settings': 'Save Settings',
                'Cancel': 'Cancel',
                'Loading...': 'Loading...',
                'English': 'English',
                'Arabic': 'العربية'
            },
            ar: {
                // Header buttons
                'Go Back': 'العودة',
                'Go Forward': 'التقدم',
                'Reload': 'إعادة التحميل',
                'Toggle Theme': 'تبديل المظهر',
                'Settings': 'الإعدادات',
                'Toggle Fullscreen': 'تبديل الشاشة الكاملة',
                'Enter YouTube URL or search...': 'أدخل رابط يوتيوب أو ابحث...',

                // Settings modal
                'Video Quality': 'جودة الفيديو',
                'Player Settings': 'إعدادات المشغل',
                'Appearance': 'المظهر',
                'Auto': 'تلقائي',
                'Autoplay': 'التشغيل التلقائي',
                'Default Volume': 'مستوى الصوت الافتراضي',
                'Theme': 'المظهر',
                'Language': 'اللغة',
                'Dark': 'داكن',
                'Light': 'فاتح',
                'Save Settings': 'حفظ الإعدادات',
                'Cancel': 'إلغاء',
                'Loading...': 'جاري التحميل...',
                'English': 'الإنجليزية',
                'Arabic': 'العربية'
            }
        };

        this.initializeElements();
        this.setupEnhancedSystems();
        this.bindEvents();
        this.loadSettings();
        this.setupElectronListeners();
        this.setupWebviewThemeListeners();
    }

    initializeElements() {
        // Main elements
        this.webview = document.getElementById('webview');
        this.urlInput = document.getElementById('urlInput');
        this.goBtn = document.getElementById('goBtn');
        
        // Navigation buttons
        this.backBtn = document.getElementById('backBtn');
        this.forwardBtn = document.getElementById('forwardBtn');
        this.reloadBtn = document.getElementById('reloadBtn');
        this.fullscreenBtn = document.getElementById('fullscreenBtn');
        
        // Settings elements
        this.settingsBtn = document.getElementById('settingsBtn');
        this.themeToggleBtn = document.getElementById('themeToggleBtn');
        this.testThemeBtn = document.getElementById('testThemeBtn');
        this.loadTestPageBtn = document.getElementById('loadTestPageBtn');
        this.forceYouTubeBtn = document.getElementById('forceYouTubeBtn');
        this.settingsModal = document.getElementById('settingsModal');
        this.closeSettingsBtn = document.getElementById('closeSettingsBtn');
        this.saveSettingsBtn = document.getElementById('saveSettingsBtn');
        this.cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
        
        // Settings form elements
        this.qualityInputs = document.querySelectorAll('input[name="quality"]');
        this.autoplayInput = document.getElementById('autoplay');
        this.volumeInput = document.getElementById('volume');
        this.volumeValue = document.getElementById('volumeValue');
        this.themeSelect = document.getElementById('theme');
        this.languageSelect = document.getElementById('language');
        
        // Quality overlay elements
        this.qualityOverlay = document.getElementById('qualityOverlay');
        this.qualityButtons = document.querySelectorAll('.quality-btn');
        this.closeQualityBtn = document.getElementById('closeQualityBtn');
        
        // Loading indicator
        this.loadingIndicator = document.getElementById('loadingIndicator');
    }

    // Setup enhanced i18n and theme systems with dynamic loading
    async setupEnhancedSystems() {
        try {
            // Dynamically import enhanced systems to avoid module conflicts
            const i18nModule = await import('./i18n/index.js');
            const ThemeManagerModule = await import('./theme/ThemeManager.js');

            this.i18n = i18nModule.default;
            this.themeManager = new ThemeManagerModule.default();

            // Setup i18n observers
            this.i18n.addObserver((event, data) => {
                this.handleI18nEvent(event, data);
            });

            // Setup theme manager observers
            this.themeManager.addObserver((event, data) => {
                this.handleThemeEvent(event, data);
            });

            this.enhancedSystemsLoaded = true;
            console.log('✅ Enhanced systems loaded successfully');

            // Initialize with system detection
            await this.detectAndApplySystemPreferences();

        } catch (error) {
            console.error('❌ Failed to load enhanced systems:', error);
            this.enhancedSystemsLoaded = false;
            // Fall back to legacy systems
            await this.initializeLegacySystems();
        }
    }

    // Handle i18n events
    handleI18nEvent(event, data) {
        switch (event) {
            case 'languageChanged':
                this.onLanguageChanged(data);
                break;
            default:
                console.log(`🌍 I18n event: ${event}`, data);
        }
    }

    // Handle theme events
    handleThemeEvent(event, data) {
        switch (event) {
            case 'themeChanged':
                this.onThemeChanged(data);
                break;
            case 'systemThemeDetected':
                this.onSystemThemeDetected(data);
                break;
            case 'systemThemeChanged':
                this.onSystemThemeChanged(data);
                break;
            case 'themeChangeStart':
                this.onThemeChangeStart(data);
                break;
            case 'themeChangeError':
                this.onThemeChangeError(data);
                break;
            default:
                console.log(`🎨 Theme event: ${event}`, data);
        }
    }

    // Initialize legacy systems as fallback
    async initializeLegacySystems() {
        console.log('🔄 Initializing legacy systems as fallback');

        // Initialize basic theme detection
        this.detectSystemTheme();

        // Initialize basic language detection
        this.detectSystemLanguage();

        // Apply initial settings
        await this.loadSettings();
    }

    // Detect and apply system preferences
    async detectAndApplySystemPreferences() {
        try {
            // Show loading state
            this.setLoadingState('settings', true);

            if (this.enhancedSystemsLoaded && this.i18n && this.themeManager) {
                // Use enhanced systems
                const currentLang = this.i18n.getCurrentLanguage();
                console.log(`🌍 Current language: ${currentLang}`);

                // Detect system theme
                await this.themeManager.loadThemePreference();

                // Apply initial translations
                this.updateAllTranslations();
            } else {
                // Use legacy systems
                console.log('🔄 Using legacy system detection');
                this.detectSystemTheme();
                this.detectSystemLanguage();
            }

        } catch (error) {
            console.error('Error detecting system preferences:', error);
        } finally {
            this.setLoadingState('settings', false);
        }
    }

    // Legacy system theme detection
    detectSystemTheme() {
        try {
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                this.currentSettings.theme = this.currentSettings.theme || 'dark';
            } else {
                this.currentSettings.theme = this.currentSettings.theme || 'light';
            }
            console.log(`🎨 System theme detected: ${this.currentSettings.theme}`);
        } catch (error) {
            console.warn('Failed to detect system theme:', error);
            this.currentSettings.theme = 'dark';
        }
    }

    // Legacy system language detection
    detectSystemLanguage() {
        try {
            const systemLang = navigator.language || navigator.userLanguage || 'en';
            const langCode = systemLang.split('-')[0].toLowerCase();

            if (langCode === 'ar') {
                this.currentSettings.language = this.currentSettings.language || 'ar';
            } else {
                this.currentSettings.language = this.currentSettings.language || 'en';
            }
            console.log(`🌍 System language detected: ${this.currentSettings.language}`);
        } catch (error) {
            console.warn('Failed to detect system language:', error);
            this.currentSettings.language = 'en';
        }
    }

    // Set loading state for different operations
    setLoadingState(operation, isLoading) {
        this.loadingStates[operation] = isLoading;

        // Update UI based on loading states
        const hasAnyLoading = Object.values(this.loadingStates).some(state => state);

        if (hasAnyLoading) {
            this.showLoading();
        } else {
            this.hideLoading();
        }

        // Add specific loading classes
        if (operation === 'language') {
            document.body.classList.toggle('language-switching', isLoading);
        }
    }

    bindEvents() {
        // Navigation events
        this.backBtn.addEventListener('click', () => this.goBack());
        this.forwardBtn.addEventListener('click', () => this.goForward());
        this.reloadBtn.addEventListener('click', () => this.reload());
        this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        
        // URL bar events
        this.goBtn.addEventListener('click', () => this.navigate());
        this.urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.navigate();
        });
        
        // Settings modal events
        this.settingsBtn.addEventListener('click', () => this.openSettings());
        this.themeToggleBtn.addEventListener('click', () => this.toggleTheme());
        this.testThemeBtn.addEventListener('click', () => this.testThemeSync());
        this.loadTestPageBtn.addEventListener('click', () => this.loadThemeTestPage());
        this.closeSettingsBtn.addEventListener('click', () => this.closeSettings());
        this.saveSettingsBtn.addEventListener('click', () => this.saveSettings());
        this.cancelSettingsBtn.addEventListener('click', () => this.closeSettings());
        
        // Settings form events
        this.qualityInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.currentQuality = e.target.value;
                this.updateQualityDisplay();
            });
        });
        
        this.volumeInput.addEventListener('input', (e) => {
            this.volumeValue.textContent = `${e.target.value}%`;
        });
        
        // Robust theme change event with fallback
        this.themeSelect.addEventListener('change', async (e) => {
            const newTheme = e.target.value;
            if (this.enhancedSystemsLoaded && this.themeManager) {
                await this.applyThemeEnhanced(newTheme);
            } else {
                this.applyTheme(newTheme);
            }
        });

        // Robust language change event with fallback
        this.languageSelect.addEventListener('change', async (e) => {
            const newLanguage = e.target.value;
            if (this.enhancedSystemsLoaded && this.i18n) {
                await this.applyLanguageEnhanced(newLanguage);
            } else {
                this.applyLanguage(newLanguage);
            }
        });

        // Quality overlay events
        this.qualityButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const quality = e.target.dataset.quality;
                this.setVideoQuality(quality);
                this.closeQualityOverlay();
            });
        });
        
        this.closeQualityBtn.addEventListener('click', () => this.closeQualityOverlay());
        
        // Webview events
        this.webview.addEventListener('did-start-loading', () => {
            this.showLoading();
        });
        this.webview.addEventListener('did-stop-loading', () => {
            this.hideLoading();
        });
        this.webview.addEventListener('did-navigate', (e) => {
            this.updateUrlBar(e.url);

            // Apply quality settings to new videos
            if (e.url.includes('/watch')) {
                setTimeout(() => {
                    this.applyQualityToYouTube();
                }, 2000);
            }
        });
        this.webview.addEventListener('did-navigate-in-page', (e) => {
            this.updateUrlBar(e.url);

            // Apply quality settings to new videos (SPA navigation)
            if (e.url.includes('/watch')) {
                setTimeout(() => {
                    this.applyQualityToYouTube();
                }, 2000);
            }
        });
        this.webview.addEventListener('dom-ready', () => {
            // Apply quality settings when DOM is ready
            this.applyQualityToYouTube();
        });
        this.webview.addEventListener('did-fail-load', (e) => {
            console.error('Webview failed to load:', e);
            this.showNotification('YouTube failed to load. Retrying with different approach...', 'info');

            // Try loading YouTube with different parameters
            setTimeout(() => {
                console.log('🔄 Retrying YouTube load with simplified URL');
                this.webview.loadURL('https://www.youtube.com');
            }, 2000);

            // If still fails after retry, load test page
            setTimeout(() => {
                if (this.webview.getURL().includes('test-theme.html')) {
                    return; // Already on test page
                }
                console.log('🧪 Loading test page as final fallback');
                this.loadThemeTestPage();
            }, 10000);
        });
        this.webview.addEventListener('crashed', () => {
            console.error('Webview crashed');
            this.showNotification('Webview crashed', 'error');
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Click outside modal to close
        this.settingsModal.addEventListener('click', (e) => {
            if (e.target === this.settingsModal) this.closeSettings();
        });
        
        this.qualityOverlay.addEventListener('click', (e) => {
            if (e.target === this.qualityOverlay) this.closeQualityOverlay();
        });
    }

    setupElectronListeners() {
        // Listen for settings open from main process
        window.electronAPI.onOpenSettings(() => {
            console.log('Received open-settings event from main process');
            this.openSettings();
        });
        
        // Listen for video quality changes from main process
        window.electronAPI.onVideoQualityChanged((quality) => {
            this.currentQuality = quality;
            this.updateQualityDisplay();
            this.applyQualityToYouTube();
        });
        
        // Set up webview event listeners for theme injection
        this.setupWebviewThemeListeners();
        
        // Test webview functionality after a short delay
        setTimeout(() => {
            this.testWebviewFunctionality();
        }, 2000);
        
        // Set up video quality monitoring
        this.setupQualityMonitoring();
    }
    
    setupWebviewThemeListeners() {
        if (!this.webview) {
            console.error('Webview not available for theme listeners');
            return;
        }

        // Listen for webview load events to apply theme and language
        this.webview.addEventListener('did-finish-load', () => {
            const currentTheme = this.getEffectiveTheme(this.currentSettings.theme || 'auto');
            const currentLanguage = this.currentSettings.language || 'ar';

            console.log(`🎨 Webview loaded, applying theme: ${currentTheme}`);

            // Apply theme immediately using both methods
            this.applyThemeToWebview(currentTheme);
            this.injectYouTubeNativeTheme(currentTheme);

            // Only ensure language on major page loads, with delay to avoid conflicts
            setTimeout(() => {
                this.ensureYouTubeLanguage(currentLanguage);
            }, 2000);
        });

        // Listen for navigation events - only apply theme, language will be handled by did-finish-load
        this.webview.addEventListener('did-navigate', () => {
            const currentTheme = this.getEffectiveTheme(this.currentSettings.theme || 'auto');
            console.log(`🎨 Webview navigated, applying theme: ${currentTheme}`);
            this.applyThemeToWebview(currentTheme);
            this.injectYouTubeNativeTheme(currentTheme);
            // Don't apply language here to avoid conflicts
        });

        // Listen for navigation in page (SPA navigation) - minimal intervention
        this.webview.addEventListener('did-navigate-in-page', () => {
            const currentTheme = this.getEffectiveTheme(this.currentSettings.theme || 'auto');
            console.log(`🎨 Webview SPA navigation, applying theme: ${currentTheme}`);
            this.applyThemeToWebview(currentTheme);
            this.injectYouTubeNativeTheme(currentTheme);
            // Don't apply language for SPA navigation to avoid excessive reloads
        });

        // DOM ready - only apply theme
        this.webview.addEventListener('dom-ready', () => {
            const currentTheme = this.getEffectiveTheme(this.currentSettings.theme || 'auto');
            console.log(`🎨 Webview DOM ready, applying theme: ${currentTheme}`);
            this.applyThemeToWebview(currentTheme);
            this.injectYouTubeNativeTheme(currentTheme);
            // Language will be handled by did-finish-load
        });

        // Stop loading - only apply theme
        this.webview.addEventListener('did-stop-loading', () => {
            const currentTheme = this.getEffectiveTheme(this.currentSettings.theme || 'auto');
            console.log(`🎨 Webview stopped loading, applying theme: ${currentTheme}`);
            this.applyThemeToWebview(currentTheme);
            this.injectYouTubeNativeTheme(currentTheme);
            // Language will be handled by did-finish-load
        });
    }

    async loadSettings() {
        try {
            this.setLoadingState('settings', true);

            this.currentSettings = await window.electronAPI.getSettings();
            this.currentQuality = this.currentSettings.videoQuality || 'auto';

            // Update UI with loaded settings
            this.updateSettingsUI();
            this.updateQualityDisplay();

            // Apply saved theme settings with robust fallback
            const savedTheme = this.currentSettings.theme || 'auto';
            if (this.enhancedSystemsLoaded && this.themeManager) {
                await this.themeManager.applyTheme(savedTheme, false);
            } else {
                this.applyTheme(savedTheme);
            }

            // Apply saved language settings with robust fallback
            const savedLanguage = this.currentSettings.language || 'ar';
            if (this.enhancedSystemsLoaded && this.i18n) {
                await this.i18n.changeLanguage(savedLanguage);
            } else {
                this.applyLanguage(savedLanguage);
            }

            // Update initial webview URL with correct language if needed
            this.updateInitialWebviewLanguage(savedLanguage);

            // Apply saved quality settings to current video
            if (this.currentQuality !== 'auto') {
                setTimeout(() => {
                    this.applyQualityToYouTube();
                }, 3000);
            }

        } catch (error) {
            console.error('Failed to load settings:', error);
            if (this.enhancedSystemsLoaded && this.i18n) {
                this.showNotification(this.t('errors.settingsLoad'), 'error');
            } else {
                this.showNotification('Failed to load settings', 'error');
            }
        } finally {
            this.setLoadingState('settings', false);
        }
    }

    updateInitialWebviewLanguage(language) {
        if (!this.webview) return;

        try {
            const currentUrl = this.webview.getURL();

            // Only update if we're still on the initial YouTube page and language doesn't match
            if (currentUrl && currentUrl.includes('youtube.com')) {
                const expectedUrl = this.addLanguageParameterToUrl(currentUrl, language);

                // If the URL needs to be updated for the correct language
                if (expectedUrl !== currentUrl) {
                    this.webview.loadURL(expectedUrl);
                }
            }
        } catch (error) {
            // Silent fail - not critical
        }
    }

    updateSettingsUI() {
        // Set quality radio button
        const qualityInput = document.getElementById(`quality-${this.currentQuality}`);
        if (qualityInput) qualityInput.checked = true;

        // Set other settings
        this.autoplayInput.checked = this.currentSettings.autoplay || false;
        this.volumeInput.value = this.currentSettings.volume || 100;
        this.volumeValue.textContent = `${this.currentSettings.volume || 100}%`;

        // Update theme and language selectors with robust fallback
        if (this.enhancedSystemsLoaded && this.themeManager) {
            this.themeSelect.value = this.themeManager.getCurrentTheme().current;
        } else {
            this.themeSelect.value = this.currentSettings.theme || 'dark';
        }

        if (this.enhancedSystemsLoaded && this.i18n) {
            this.languageSelect.value = this.i18n.getCurrentLanguage();
        } else {
            this.languageSelect.value = this.currentSettings.language || 'ar';
        }

        // Update all translations
        this.updateAllTranslations();
    }

    updateQualityDisplay() {
        // Update quality buttons in overlay
        this.qualityButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.quality === this.currentQuality) {
                btn.classList.add('active');
            }
        });
    }
    
    // Enhanced theme application using ThemeManager
    async applyThemeEnhanced(theme) {
        try {
            this.setLoadingState('theme', true);

            const success = await this.themeManager.applyTheme(theme);

            if (success) {
                // Update settings
                this.currentSettings.theme = theme;

                // Apply theme to webview content
                this.applyThemeToWebview(this.themeManager.getEffectiveTheme());

                // Show success notification
                this.showNotification(this.t('notifications.themeChanged', { theme }), 'success');
            } else {
                throw new Error('Theme application failed');
            }
        } catch (error) {
            console.error('Error applying theme:', error);
            this.showNotification(this.t('errors.themeApply'), 'error');
        } finally {
            this.setLoadingState('theme', false);
        }
    }

    // Robust theme application with enhanced and legacy support
    applyTheme(theme) {
        if (this.enhancedSystemsLoaded && this.themeManager) {
            // Use enhanced method
            return this.applyThemeEnhanced(theme);
        } else {
            // Use legacy method
            return this.applyThemeLegacy(theme);
        }
    }

    // Legacy theme application method
    applyThemeLegacy(theme) {
        try {
            console.log(`🎨 Applying theme (legacy): ${theme}`);

            // Remove existing theme classes
            document.body.removeAttribute('data-theme');

            // Apply new theme
            if (theme === 'auto') {
                // Auto theme follows system preference
                const systemDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
                document.body.setAttribute('data-theme', systemDark ? 'dark' : 'light');
            } else {
                document.body.setAttribute('data-theme', theme);
            }

            // Store theme preference
            this.currentSettings.theme = theme;

            // Update theme toggle button icon
            this.updateThemeToggleIcon();

            // Apply theme to webview content with enhanced timing
            this.applyThemeToWebview(theme === 'auto' ?
                (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') :
                theme);

            // Show notification
            this.showNotification(`Theme changed to ${theme}`, 'info');

            return true;
        } catch (error) {
            console.error('Error applying theme (legacy):', error);
            this.showNotification('Failed to apply theme', 'error');
            return false;
        }
    }

    // Enhanced language application using i18n system
    async applyLanguageEnhanced(language) {
        try {
            this.setLoadingState('language', true);

            const success = await this.i18n.changeLanguage(language);

            if (success) {
                // Update settings
                this.currentSettings.language = language;

                // Apply language to YouTube webview content
                this.applyLanguageToYouTube(language);

                // Show success notification
                this.showNotification(this.t('notifications.languageChanged'), 'success');
            } else {
                throw new Error('Language change failed');
            }
        } catch (error) {
            console.error('Error applying language:', error);
            this.showNotification(this.t('errors.languageApply'), 'error');
        } finally {
            this.setLoadingState('language', false);
        }
    }

    // Robust language application with enhanced and legacy support
    applyLanguage(language) {
        if (this.enhancedSystemsLoaded && this.i18n) {
            // Use enhanced method
            return this.applyLanguageEnhanced(language);
        } else {
            // Use legacy method
            return this.applyLanguageLegacy(language);
        }
    }

    // Legacy language application method
    applyLanguageLegacy(language) {
        try {
            console.log(`🌍 Applying language (legacy): ${language}`);

            // Set document direction for RTL support
            if (language === 'ar') {
                document.documentElement.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
                document.body.setAttribute('dir', 'rtl');
            } else {
                document.documentElement.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', 'en');
                document.body.setAttribute('dir', 'ltr');
            }

            // Store language preference
            this.currentSettings.language = language;

            // Apply translations to all UI elements
            this.applyTranslations(language);

            // Reset language application state before applying new language
            this.resetLanguageApplicationState();

            // Apply language to YouTube webview content
            this.applyLanguageToYouTube(language);

            // Show notification
            const message = language === 'ar' ? 'تم تغيير اللغة إلى العربية' : 'Language changed to English';
            this.showNotification(message, 'info');

            return true;
        } catch (error) {
            console.error('Error applying language (legacy):', error);
            this.showNotification('Failed to change language', 'error');
            return false;
        }
    }

    resetLanguageApplicationState() {
        this.isApplyingLanguage = false;
        this.lastAppliedLanguage = null;
    }

    applyTranslations(language) {
        const t = this.translations[language] || this.translations.en;

        // Update button titles
        document.getElementById('backBtn').setAttribute('title', t['Go Back']);
        document.getElementById('forwardBtn').setAttribute('title', t['Go Forward']);
        document.getElementById('reloadBtn').setAttribute('title', t['Reload']);
        document.getElementById('themeToggleBtn').setAttribute('title', t['Toggle Theme']);
        document.getElementById('settingsBtn').setAttribute('title', t['Settings']);
        document.getElementById('fullscreenBtn').setAttribute('title', t['Toggle Fullscreen']);

        // Update input placeholder
        document.getElementById('urlInput').setAttribute('placeholder', t['Enter YouTube URL or search...']);

        // Update settings modal content
        document.querySelector('.modal-header h2').textContent = t['Settings'];

        // Update section headers
        const sections = document.querySelectorAll('.settings-section h3');
        if (sections[0]) sections[0].textContent = t['Video Quality'];
        if (sections[1]) sections[1].textContent = t['Player Settings'];
        if (sections[2]) sections[2].textContent = t['Appearance'];

        // Update quality labels
        document.querySelectorAll('.quality-label').forEach(label => {
            const value = label.textContent.trim();
            if (value === 'Auto' || value === 'تلقائي') {
                label.textContent = t['Auto'];
            }
        });

        // Update setting labels
        document.querySelector('label[for="autoplay"]').textContent = t['Autoplay'];
        document.querySelector('label[for="volume"]').textContent = t['Default Volume'];
        document.querySelector('label[for="theme"]').textContent = t['Theme'];
        document.querySelector('label[for="language"]').textContent = t['Language'];

        // Update theme options
        const themeOptions = document.querySelectorAll('#theme option');
        if (themeOptions[0]) themeOptions[0].textContent = t['Dark'];
        if (themeOptions[1]) themeOptions[1].textContent = t['Light'];
        if (themeOptions[2]) themeOptions[2].textContent = t['Auto'];

        // Update language options
        const languageOptions = document.querySelectorAll('#language option');
        if (languageOptions[0]) languageOptions[0].textContent = t['Arabic'];
        if (languageOptions[1]) languageOptions[1].textContent = t['English'];

        // Update buttons
        document.getElementById('saveSettingsBtn').textContent = t['Save Settings'];
        document.getElementById('cancelSettingsBtn').textContent = t['Cancel'];

        // Update quality overlay
        document.querySelector('.quality-panel h3').textContent = t['Video Quality'];
        document.querySelectorAll('.quality-btn').forEach(btn => {
            const quality = btn.dataset.quality;
            if (quality === 'auto') {
                btn.textContent = t['Auto'];
            }
        });

        // Update loading indicator
        const loadingText = document.querySelector('#loadingIndicator span');
        if (loadingText) loadingText.textContent = t['Loading...'];
    }

    applyLanguageToYouTube(language) {
        // Prevent recursive language applications
        if (this.isApplyingLanguage || this.lastAppliedLanguage === language) {
            return;
        }

        if (!this.webview) {
            return;
        }

        // Set flag to prevent recursive calls
        this.isApplyingLanguage = true;
        this.lastAppliedLanguage = language;

        // Try to apply language with a single attempt
        this.performLanguageApplication(language);
    }

    performLanguageApplication(language) {
        let currentUrl;
        try {
            currentUrl = this.webview.getURL();
        } catch (error) {
            // Webview not ready, try once more after delay
            setTimeout(() => {
                this.performLanguageApplication(language);
            }, 1500);
            return;
        }

        // If URL is empty or not loaded yet, wait once more
        if (!currentUrl || currentUrl === 'about:blank' || currentUrl === '') {
            setTimeout(() => {
                this.performLanguageApplication(language);
            }, 1500);
            return;
        }

        // Apply language to YouTube URL
        if (currentUrl.includes('youtube.com')) {
            const modifiedUrl = this.addLanguageParameterToUrl(currentUrl, language);

            if (modifiedUrl !== currentUrl) {
                // Show notification
                const message = language === 'ar' ?
                    'تطبيق اللغة العربية على يوتيوب...' :
                    'Applying English language to YouTube...';
                this.showNotification(message, 'info');

                // Navigate to URL with language parameter
                this.webview.loadURL(modifiedUrl);

                // Reset flag after navigation
                setTimeout(() => {
                    this.isApplyingLanguage = false;
                }, 3000);
            } else {
                // URL already correct, reset flag immediately
                this.isApplyingLanguage = false;
            }
        } else {
            // Not a YouTube URL, reset flag
            this.isApplyingLanguage = false;
        }
    }

    addLanguageParameterToUrl(url, language) {
        try {
            const urlObj = new URL(url);
            urlObj.searchParams.set('hl', language);
            urlObj.searchParams.set('gl', language === 'ar' ? 'EG' : 'US');

            // Enhanced language parameters for better YouTube integration
            if (language === 'ar') {
                urlObj.searchParams.set('persist_hl', '1');
                urlObj.searchParams.set('persist_gl', '1');
            }

            return urlObj.toString();
        } catch (error) {
            console.error('Error adding language parameters:', error);
            return url;
        }
    }

    ensureYouTubeLanguage(language) {
        // Don't interfere if language is already being applied
        if (this.isApplyingLanguage) {
            return;
        }

        if (!this.webview) return;

        try {
            const currentUrl = this.webview.getURL();

            if (currentUrl && currentUrl.includes('youtube.com')) {
                // Check if the URL already has the correct language parameter
                const urlObj = new URL(currentUrl);
                const currentLang = urlObj.searchParams.get('hl');

                // Only apply if language is significantly different and we're not already applying
                if (currentLang !== language && !this.isApplyingLanguage) {
                    // Use the main language application method to maintain consistency
                    this.applyLanguageToYouTube(language);
                }
            }
        } catch (error) {
            // Silent fail
        }
    }

    injectYouTubeLanguage(language) {
        if (!this.webview) return;

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;

            const jsCode = `
                (function() {
                    try {
                        // Set document language
                        document.documentElement.lang = '${language}';

                        // Set language preference in localStorage
                        try {
                            localStorage.setItem('yt-player-language', '${language}');
                            localStorage.setItem('yt-language', '${language}');
                        } catch (e) {
                            // localStorage might not be available
                        }

                        // Check current URL and add language parameter if missing
                        const currentUrl = window.location.href;

                        if (!currentUrl.includes('hl=${language}')) {
                            const url = new URL(currentUrl);
                            url.searchParams.set('hl', '${language}');

                            // Navigate to URL with language parameter
                            if (url.toString() !== currentUrl) {
                                window.location.href = url.toString();
                                return;
                            }
                        }

                    } catch (error) {
                        // Silent fail
                    }
                })();
            `;

            webContents.executeJavaScript(jsCode).catch(() => {
                // Silent fail
            });

        } catch (error) {
            // Silent fail
        }
    }

    applyThemeToWebview(theme) {
        // Enhanced theme application with comprehensive approach
        const effectiveTheme = this.getEffectiveTheme(theme);

        console.log(`🎨 Applying theme to webview: ${theme} (effective: ${effectiveTheme})`);

        // Method 1: Apply using native YouTube theme system (primary method)
        this.injectYouTubeNativeTheme(effectiveTheme);

        // Method 2: Apply CSS-based theme (fallback)
        this.applyThemeImmediate(effectiveTheme);

        // Method 3: Retry with progressive delays to ensure application
        const retryDelays = [100, 300, 600, 1000, 2000];
        retryDelays.forEach((delay, index) => {
            setTimeout(async () => {
                console.log(`🔄 Theme retry ${index + 1} for ${effectiveTheme}`);
                await this.injectYouTubeNativeTheme(effectiveTheme);
                this.applyThemeImmediate(effectiveTheme);
            }, delay);
        });

        // Method 4: Final verification and force application
        setTimeout(async () => {
            const result = await this.verifyThemeApplication(effectiveTheme);
            if (!result.success) {
                console.warn(`⚠️ Theme verification failed, forcing application`);
                this.forceThemeViaDOM(effectiveTheme);
                await this.injectYouTubeNativeTheme(effectiveTheme);
            }
        }, 3000);
    }

    // Get effective theme (resolve auto theme)
    getEffectiveTheme(theme) {
        if (theme === 'auto') {
            if (this.enhancedSystemsLoaded && this.themeManager) {
                return this.themeManager.getEffectiveTheme();
            } else {
                // Legacy auto theme detection
                return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            }
        }
        return theme;
    }

    // Verify that theme was actually applied to YouTube
    async verifyThemeApplication(expectedTheme) {
        if (!this.webview) return { success: false, reason: 'No webview' };

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return { success: false, reason: 'No webContents' };

            const jsCode = `
                (function() {
                    try {
                        const expectedTheme = '${expectedTheme}';
                        const isDarkExpected = expectedTheme === 'dark';

                        // Check HTML attributes
                        const html = document.documentElement;
                        const hasDarkAttribute = html.hasAttribute('dark');
                        const dataTheme = html.getAttribute('data-theme');

                        // Check ytd-app
                        const ytdApp = document.querySelector('ytd-app');
                        const ytdAppHasDark = ytdApp ? ytdApp.hasAttribute('dark') : false;

                        // Check computed styles
                        const bodyStyles = window.getComputedStyle(document.body);
                        const backgroundColor = bodyStyles.backgroundColor;

                        // Determine if theme is actually applied
                        const isDarkActual = hasDarkAttribute || dataTheme === 'dark' || ytdAppHasDark;
                        const themeMatches = isDarkExpected === isDarkActual;

                        // Check background color as additional verification
                        const bgColorMatches = isDarkExpected ?
                            (backgroundColor.includes('15, 15, 15') || backgroundColor.includes('0, 0, 0')) :
                            (backgroundColor.includes('255, 255, 255') || backgroundColor === 'rgb(255, 255, 255)');

                        return {
                            success: themeMatches && bgColorMatches,
                            expectedTheme,
                            actualState: {
                                hasDarkAttribute,
                                dataTheme,
                                ytdAppHasDark,
                                backgroundColor,
                                isDarkActual
                            },
                            themeMatches,
                            bgColorMatches
                        };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                })();
            `;

            const result = await webContents.executeJavaScript(jsCode);

            if (!result.success) {
                console.warn(`⚠️ Theme verification failed for ${expectedTheme}:`, result);
            } else {
                console.log(`✅ Theme verification passed for ${expectedTheme}`);
            }

            return result;

        } catch (error) {
            console.error('Error verifying theme application:', error);
            return { success: false, error: error.message };
        }
    }
    
    applyThemeImmediate(theme) {
        if (!this.webview) return;

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;

            // Remove any previously injected theme CSS
            if (this.injectedThemeKey) {
                webContents.removeInsertedCSS(this.injectedThemeKey).catch(() => {});
            }

            // Create CSS for the selected theme
            const themeCSS = this.generateThemeCSS(theme);

            // Use YouTube's native theme system first
            this.injectYouTubeNativeTheme(theme);

            // Inject the CSS as fallback
            webContents.insertCSS(themeCSS, { cssOrigin: 'author' })
                .then(key => {
                    this.injectedThemeKey = key;
                    console.log(`🎨 Theme CSS injected successfully: ${theme}`);
                })
                .catch(() => {
                    // Try JavaScript injection as fallback
                    this.injectThemeViaJavaScript(theme);
                    this.forceThemeViaDOM(theme);
                });

        } catch (error) {
            // Try JavaScript injection as fallback
            this.injectThemeViaJavaScript(theme);
            this.forceThemeViaDOM(theme);
        }
    }
    
    injectThemeViaJavaScript(theme) {
        if (!this.webview) return;
        
        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;
            
            const jsCode = `
                (function() {
                    try {
                        // Remove any existing theme styles
                        const existingStyles = document.querySelectorAll('style[data-youtube-theme]');
                        existingStyles.forEach(style => style.remove());
                        
                        // Create new style element
                        const style = document.createElement('style');
                        style.setAttribute('data-youtube-theme', '${theme}');
                        style.textContent = \`${this.generateThemeCSS(theme)}\`;
                        
                        // Insert at the beginning of head for highest priority
                        document.head.insertBefore(style, document.head.firstChild);
                        
                        // Force immediate repaint
                        document.body.style.display = 'none';
                        document.body.offsetHeight; // Force reflow
                        document.body.style.display = '';
                        
                        // Also apply theme directly to key elements
                        const keyElements = [
                            'html', 'body', '#page-manager', '#content', '#primary', 
                            '#secondary', 'ytd-masthead', '#masthead-container'
                        ];
                        
                        keyElements.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                if ('${theme}' === 'dark') {
                                    el.style.backgroundColor = '#0f0f0f';
                                    el.style.color = '#ffffff';
                                } else {
                                    el.style.backgroundColor = '#ffffff';
                                    el.style.color = '#0f0f0f';
                                }
                            });
                        });
                        
                    } catch (error) {
                        console.error('Error applying theme via JavaScript:', error);
                    }
                })();
            `;

            webContents.executeJavaScript(jsCode).catch(() => {});

        } catch (error) {
            // Silent fail
        }
    }
    
    forceThemeViaDOM(theme) {
        if (!this.webview) return;
        
        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;
            
            const jsCode = `
                (function() {
                    try {
                        const isDark = '${theme}' === 'dark';
                        
                        // Force theme on root elements first
                        document.documentElement.style.setProperty('background-color', isDark ? '#0f0f0f' : '#ffffff', 'important');
                        document.documentElement.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                        document.body.style.setProperty('background-color', isDark ? '#0f0f0f' : '#ffffff', 'important');
                        document.body.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                        
                        // Target YouTube-specific elements
                        const youtubeSelectors = [
                            '#page-manager', '#content', '#primary', '#secondary',
                            'ytd-app', 'ytd-page-manager', 'ytd-browse', 'ytd-watch',
                            'ytd-masthead', '#masthead-container', '#header',
                            'ytd-guide-renderer', 'ytd-guide-section-renderer',
                            'ytd-video-renderer', 'ytd-compact-video-renderer',
                            'ytd-rich-item-renderer', 'ytd-thumbnail'
                        ];
                        
                        youtubeSelectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                el.style.setProperty('background-color', isDark ? '#0f0f0f' : '#ffffff', 'important');
                                el.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                            });
                        });
                        
                        // Force theme on all text elements
                        const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, a, yt-formatted-string');
                        textElements.forEach(el => {
                            el.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                        });
                        
                        // Force theme on input elements
                        const inputElements = document.querySelectorAll('input, textarea, select, #search-input');
                        inputElements.forEach(el => {
                            el.style.setProperty('background-color', isDark ? '#2a2a2a' : '#ffffff', 'important');
                            el.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                            el.style.setProperty('border-color', isDark ? '#444444' : '#cccccc', 'important');
                        });
                        
                        // Force theme on buttons
                        const buttonElements = document.querySelectorAll('button, .ytp-button, ytd-button-renderer');
                        buttonElements.forEach(el => {
                            el.style.setProperty('background-color', isDark ? '#4a4a4a' : '#f8f9fa', 'important');
                            el.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                        });
                        
                        // Special handling for media elements - keep them transparent
                        const mediaElements = document.querySelectorAll('img, video, .ytp-pause-overlay, .ytp-cued-thumbnail-overlay');
                        mediaElements.forEach(el => {
                            el.style.setProperty('background-color', 'transparent', 'important');
                            el.style.setProperty('filter', 'none', 'important');
                        });
                        
                        // Force repaint
                        document.body.style.display = 'none';
                        document.body.offsetHeight; // Force reflow
                        document.body.style.display = '';
                        
                        console.log('Theme forced via enhanced DOM manipulation: ${theme}');
                    } catch (error) {
                        console.error('Error in enhanced DOM theme forcing:', error);
                    }
                })();
            `;
            
            webContents.executeJavaScript(jsCode).catch(err => {
                console.log('⚠️ Enhanced DOM theme forcing failed:', err.message);
            });
            
        } catch (error) {
            console.log('⚠️ Error in enhanced DOM theme forcing:', error.message);
        }
    }
    


    
    generateThemeCSS(theme) {
        // Enhanced CSS that works with YouTube's native theme system
        if (theme === 'dark') {
            return `
                /* YouTube Dark Theme Enhancement */
                html[dark], html[data-theme="dark"] {
                    color-scheme: dark !important;
                }

                /* Ensure dark theme is properly applied */
                html, html[dark] {
                    color-scheme: dark !important;
                    background-color: #0f0f0f !important;
                }

                /* Enhanced ytd-app dark theme support */
                ytd-app[dark], ytd-app[data-theme="dark"], html[dark] ytd-app {
                    --yt-spec-base-background: #0f0f0f !important;
                    --yt-spec-raised-background: #212121 !important;
                    --yt-spec-menu-background: #282828 !important;
                    --yt-spec-text-primary: #ffffff !important;
                    --yt-spec-text-secondary: #aaaaaa !important;
                    --yt-spec-general-background-a: #0f0f0f !important;
                    --yt-spec-general-background-b: #212121 !important;
                    --yt-spec-wordmark-text: #ffffff !important;
                    --yt-spec-call-to-action: #3ea6ff !important;
                    --yt-spec-icon-active-other: #ffffff !important;
                    --yt-spec-icon-inactive: #aaaaaa !important;
                    background-color: #0f0f0f !important;
                    color: #ffffff !important;
                }

                /* Force dark theme on body and main containers */
                html[dark] body, body {
                    background-color: #0f0f0f !important;
                    color: #ffffff !important;
                }

                /* Ensure all YouTube containers use dark theme */
                html[dark] #page-manager,
                html[dark] #content,
                html[dark] #primary,
                html[dark] #secondary {
                    background-color: #0f0f0f !important;
                    color: #ffffff !important;
                }
            `;
        } else {
            return `
                /* YouTube Light Theme Enhancement */
                html:not([dark]), html[data-theme="light"] {
                    color-scheme: light !important;
                }

                /* Ensure light theme is properly applied */
                html, html:not([dark]) {
                    color-scheme: light !important;
                    background-color: #ffffff !important;
                }

                /* Enhanced ytd-app light theme support */
                ytd-app:not([dark]), ytd-app[data-theme="light"], html:not([dark]) ytd-app {
                    --yt-spec-base-background: #ffffff !important;
                    --yt-spec-raised-background: #f9f9f9 !important;
                    --yt-spec-menu-background: #ffffff !important;
                    --yt-spec-text-primary: #0f0f0f !important;
                    --yt-spec-text-secondary: #606060 !important;
                    --yt-spec-general-background-a: #ffffff !important;
                    --yt-spec-general-background-b: #f9f9f9 !important;
                    --yt-spec-wordmark-text: #000000 !important;
                    --yt-spec-call-to-action: #065fd4 !important;
                    --yt-spec-icon-active-other: #030303 !important;
                    --yt-spec-icon-inactive: #606060 !important;
                    background-color: #ffffff !important;
                    color: #0f0f0f !important;
                }

                /* Force light theme on body and main containers */
                html:not([dark]) body, body {
                    background-color: #ffffff !important;
                    color: #0f0f0f !important;
                }

                /* Ensure all YouTube containers use light theme */
                html:not([dark]) #page-manager,
                html:not([dark]) #content,
                html:not([dark]) #primary,
                html:not([dark]) #secondary {
                    background-color: #ffffff !important;
                    color: #0f0f0f !important;
                }
            `;
        }
    }

    // Research-based YouTube theme application using actual mechanisms
    async injectYouTubeNativeTheme(theme) {
        if (!this.webview) return;

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;

            // First, investigate the current state
            const investigation = await this.inspectYouTubeThemeStructure();

            const jsCode = `
                (function() {
                    try {
                        console.log('🎨 Applying YouTube theme using research-based approach: ${theme}');

                        const isDark = '${theme}' === 'dark';
                        const results = {
                            methods: {},
                            finalState: {},
                            success: false
                        };

                        // Method 1: YouTube's documented theme system
                        // Based on investigation: YouTube uses 'dark' attribute on html element
                        const html = document.documentElement;
                        try {
                            if (isDark) {
                                html.setAttribute('dark', '');
                                html.removeAttribute('light');
                            } else {
                                html.removeAttribute('dark');
                                html.setAttribute('light', '');
                            }
                            results.methods.htmlAttribute = 'success';
                        } catch (e) {
                            results.methods.htmlAttribute = 'failed: ' + e.message;
                        }

                        // Method 2: YouTube's data-theme system (if it exists)
                        try {
                            html.setAttribute('data-theme', isDark ? 'dark' : 'light');
                            results.methods.dataTheme = 'success';
                        } catch (e) {
                            results.methods.dataTheme = 'failed: ' + e.message;
                        }

                        // Method 3: CSS color-scheme (modern standard)
                        try {
                            html.style.colorScheme = isDark ? 'dark' : 'light';
                            if (document.body) {
                                document.body.style.colorScheme = isDark ? 'dark' : 'light';
                            }
                            results.methods.colorScheme = 'success';
                        } catch (e) {
                            results.methods.colorScheme = 'failed: ' + e.message;
                        }

                        // Method 4: ytd-app element (YouTube's main container)
                        const ytdApp = document.querySelector('ytd-app');
                        if (ytdApp) {
                            try {
                                if (isDark) {
                                    ytdApp.setAttribute('dark', '');
                                    ytdApp.removeAttribute('light');
                                } else {
                                    ytdApp.removeAttribute('dark');
                                    ytdApp.setAttribute('light', '');
                                }
                                results.methods.ytdApp = 'success';
                            } catch (e) {
                                results.methods.ytdApp = 'failed: ' + e.message;
                            }
                        } else {
                            results.methods.ytdApp = 'no ytd-app element found';
                        }

                        // Method 5: YouTube's localStorage theme preference
                        try {
                            // Research actual YouTube localStorage keys
                            const themeValue = isDark ? 'DARK' : 'LIGHT';

                            // Try different possible keys YouTube might use
                            const possibleKeys = [
                                'yt-remote-device-id',
                                'yt-player-theme',
                                'yt-theme-preference',
                                'youtube-theme'
                            ];

                            possibleKeys.forEach(key => {
                                try {
                                    localStorage.setItem(key, JSON.stringify({
                                        data: themeValue,
                                        expiration: Date.now() + (365 * 24 * 60 * 60 * 1000),
                                        creation: Date.now()
                                    }));
                                } catch (e) {
                                    // Try simple string value
                                    localStorage.setItem(key, themeValue);
                                }
                            });

                            results.methods.localStorage = 'success';
                        } catch (e) {
                            results.methods.localStorage = 'failed: ' + e.message;
                        }

                        // Method 6: YouTube's internal configuration
                        if (window.yt && window.yt.config_) {
                            try {
                                window.yt.config_.EXPERIMENT_FLAGS = window.yt.config_.EXPERIMENT_FLAGS || {};
                                window.yt.config_.EXPERIMENT_FLAGS.web_dark_theme = isDark;
                                window.yt.config_.EXPERIMENT_FLAGS.kevlar_watch_color_update = true;
                                results.methods.ytConfig = 'success';
                            } catch (e) {
                                results.methods.ytConfig = 'failed: ' + e.message;
                            }
                        } else {
                            results.methods.ytConfig = 'no yt.config found';
                        }

                        // Method 7: Trigger YouTube's theme change events
                        try {
                            // Dispatch various events that might trigger theme updates
                            const events = [
                                new CustomEvent('theme-changed', { detail: { theme: isDark ? 'dark' : 'light' } }),
                                new CustomEvent('yt-theme-update', { detail: { isDark } }),
                                new Event('storage') // Simulate localStorage change
                            ];

                            events.forEach(event => {
                                document.dispatchEvent(event);
                                if (ytdApp) ytdApp.dispatchEvent(event);
                            });

                            results.methods.events = 'success';
                        } catch (e) {
                            results.methods.events = 'failed: ' + e.message;
                        }

                        // Method 8: Force CSS variables update (as fallback)
                        if (ytdApp) {
                            try {
                                const themeVars = isDark ? {
                                    '--yt-spec-base-background': '#0f0f0f',
                                    '--yt-spec-raised-background': '#212121',
                                    '--yt-spec-menu-background': '#282828',
                                    '--yt-spec-text-primary': '#ffffff',
                                    '--yt-spec-text-secondary': '#aaaaaa'
                                } : {
                                    '--yt-spec-base-background': '#ffffff',
                                    '--yt-spec-raised-background': '#f9f9f9',
                                    '--yt-spec-menu-background': '#ffffff',
                                    '--yt-spec-text-primary': '#0f0f0f',
                                    '--yt-spec-text-secondary': '#606060'
                                };

                                Object.entries(themeVars).forEach(([prop, value]) => {
                                    ytdApp.style.setProperty(prop, value);
                                });

                                results.methods.cssVariables = 'success';
                            } catch (e) {
                                results.methods.cssVariables = 'failed: ' + e.message;
                            }
                        }

                        // Method 9: Force repaint
                        try {
                            if (document.body) {
                                const display = document.body.style.display;
                                document.body.style.display = 'none';
                                document.body.offsetHeight; // Force reflow
                                document.body.style.display = display || '';
                            }
                            results.methods.repaint = 'success';
                        } catch (e) {
                            results.methods.repaint = 'failed: ' + e.message;
                        }

                        // Capture final state for verification
                        results.finalState = {
                            htmlHasDark: html.hasAttribute('dark'),
                            htmlHasLight: html.hasAttribute('light'),
                            htmlDataTheme: html.getAttribute('data-theme'),
                            htmlColorScheme: html.style.colorScheme,
                            bodyColorScheme: document.body ? document.body.style.colorScheme : null,
                            ytdAppHasDark: ytdApp ? ytdApp.hasAttribute('dark') : null,
                            ytdAppHasLight: ytdApp ? ytdApp.hasAttribute('light') : null
                        };

                        // Determine overall success
                        const successfulMethods = Object.values(results.methods).filter(v => v === 'success').length;
                        results.success = successfulMethods > 0;

                        console.log(\`🎨 YouTube theme application results:\`, results);
                        return results;
                    } catch (error) {
                        console.error('❌ Error in YouTube theme application:', error);
                        return { success: false, error: error.message, stack: error.stack };
                    }
                })();
            `;

            const result = await webContents.executeJavaScript(jsCode);
            console.log('🎨 Research-based YouTube theme result:', result);

            // Log the investigation results for analysis
            if (investigation) {
                console.log('🔍 YouTube investigation informed our approach:', {
                    isYouTube: investigation.pageInfo?.isYouTube,
                    hasYtdApp: !!investigation.ytdAppElement?.tagName,
                    currentThemeState: {
                        htmlDark: investigation.htmlElement?.attributes?.dark,
                        htmlDataTheme: investigation.htmlElement?.attributes?.['data-theme'],
                        ytdAppDark: investigation.ytdAppElement?.attributes?.dark
                    }
                });
            }

            return result?.success || false;

        } catch (error) {
            console.error('Error in research-based YouTube theme injection:', error);
            return false;
        }
    }

    // Robust theme toggle with enhanced and legacy support
    async toggleTheme() {
        try {
            if (this.enhancedSystemsLoaded && this.themeManager) {
                // Use enhanced theme manager
                const success = await this.themeManager.toggleTheme();

                if (success) {
                    const currentTheme = this.themeManager.getCurrentTheme();

                    // Update settings
                    this.currentSettings.theme = currentTheme.current;

                    // Apply theme to webview with enhanced timing
                    this.applyThemeToWebview(currentTheme.effective);

                    // Update theme select in settings
                    if (this.themeSelect) {
                        this.themeSelect.value = currentTheme.current;
                    }

                    console.log(`🎨 Theme toggled to: ${currentTheme.current} (effective: ${currentTheme.effective})`);
                }
            } else {
                // Use legacy theme toggle
                this.toggleThemeLegacy();
            }
        } catch (error) {
            console.error('Error toggling theme:', error);
            const errorMsg = this.enhancedSystemsLoaded && this.i18n ?
                this.t('errors.themeApply') : 'Failed to toggle theme';
            this.showNotification(errorMsg, 'error');
        }
    }

    // Legacy theme toggle method
    toggleThemeLegacy() {
        const currentTheme = this.currentSettings.theme || 'dark';
        let newTheme;

        if (currentTheme === 'dark') {
            newTheme = 'light';
        } else if (currentTheme === 'light') {
            newTheme = 'auto';
        } else {
            newTheme = 'dark';
        }

        console.log(`🎨 Toggling theme from ${currentTheme} to ${newTheme} (legacy)`);
        this.applyThemeLegacy(newTheme);

        // Update theme select in settings
        if (this.themeSelect) {
            this.themeSelect.value = newTheme;
        }
    }
    
    updateThemeToggleIcon() {
        const icon = this.themeToggleBtn.querySelector('i');
        if (!icon) return;

        if (this.enhancedSystemsLoaded && this.themeManager) {
            // Use enhanced theme manager
            const currentTheme = this.themeManager.getCurrentTheme();
            const nextTheme = this.themeManager.getNextTheme();

            // Set icon based on current theme
            if (currentTheme.current === 'light') {
                icon.className = 'fas fa-sun';
            } else if (currentTheme.current === 'auto') {
                icon.className = 'fas fa-adjust';
            } else {
                icon.className = 'fas fa-moon';
            }

            // Set title for next theme
            const title = this.i18n ? this.t('header.toggleTheme') : 'Toggle Theme';
            this.themeToggleBtn.title = `${title} (${nextTheme.name})`;
        } else {
            // Use legacy theme detection
            const currentTheme = this.currentSettings.theme || 'dark';

            if (currentTheme === 'light') {
                icon.className = 'fas fa-sun';
                this.themeToggleBtn.title = 'Switch to Auto Theme';
            } else if (currentTheme === 'auto') {
                icon.className = 'fas fa-adjust';
                this.themeToggleBtn.title = 'Switch to Dark Theme';
            } else {
                icon.className = 'fas fa-moon';
                this.themeToggleBtn.title = 'Switch to Light Theme';
            }
        }
    }

    // Comprehensive theme synchronization testing with investigation
    async testThemeSync() {
        console.log('🧪 Starting comprehensive theme synchronization test...');
        this.showNotification('Starting comprehensive theme test...', 'info');

        // Step 1: Deep investigation of YouTube's current state
        console.log('🔍 Step 1: Investigating YouTube structure...');
        const initialInvestigation = await this.inspectYouTubeThemeStructure();

        if (initialInvestigation) {
            console.log('📊 Initial YouTube State:', {
                isYouTube: initialInvestigation.pageInfo?.isYouTube,
                currentTheme: initialInvestigation.htmlElement?.attributes?.dark ? 'dark' : 'light',
                hasYtdApp: !!initialInvestigation.ytdAppElement?.tagName,
                cssVariables: initialInvestigation.ytdAppElement?.cssVariables
            });
        }

        const themes = [
            { name: 'dark', displayName: 'Dark Mode' },
            { name: 'light', displayName: 'Light Mode' },
            { name: 'auto', displayName: 'Auto Mode' }
        ];

        let currentIndex = 0;

        const testNextTheme = async () => {
            if (currentIndex >= themes.length) {
                console.log('✅ Comprehensive theme sync test completed');
                this.showNotification('✅ Comprehensive theme test completed!', 'success');

                // Final investigation
                console.log('🔍 Final state investigation...');
                await this.inspectYouTubeThemeStructure();
                return;
            }

            const theme = themes[currentIndex];
            console.log(`\n🧪 Testing ${theme.displayName} (${theme.name})...`);
            this.showNotification(`Testing ${theme.displayName}...`, 'info');

            // Step 2: Apply theme using app's system
            console.log('🎨 Step 2: Applying theme via app system...');
            if (this.enhancedSystemsLoaded && this.themeManager) {
                await this.themeManager.applyTheme(theme.name, false);
            } else {
                this.applyThemeLegacy(theme.name);
            }

            // Step 3: Apply to webview using our enhanced method
            const effectiveTheme = this.getEffectiveTheme(theme.name);
            console.log(`🎯 Step 3: Applying to webview (effective: ${effectiveTheme})...`);

            // Try our research-based approach
            const injectionResult = await this.injectYouTubeNativeTheme(effectiveTheme);
            console.log('💉 Injection result:', injectionResult);

            // Step 4: Try YouTube's native switching mechanism
            console.log('🔄 Step 4: Attempting YouTube native switch...');
            const nativeSwitchResult = await this.tryYouTubeNativeThemeSwitch(effectiveTheme);
            console.log('🔄 Native switch result:', nativeSwitchResult);

            // Step 5: Apply CSS fallback
            console.log('🎨 Step 5: Applying CSS fallback...');
            this.applyThemeToWebview(effectiveTheme);

            // Step 6: Wait and verify
            setTimeout(async () => {
                console.log('🔍 Step 6: Verifying theme application...');

                // Detailed verification
                const verification = await this.verifyThemeApplication(effectiveTheme);
                console.log('✅ Verification result:', verification);

                // Post-change investigation
                const postInvestigation = await this.inspectYouTubeThemeAfterChange(effectiveTheme);
                console.log('📊 Post-change investigation:', postInvestigation);

                // Report results
                if (verification.success) {
                    console.log(`✅ ${theme.displayName} test PASSED`);
                    this.showNotification(`✅ ${theme.displayName} test PASSED`, 'success');
                } else {
                    console.warn(`❌ ${theme.displayName} test FAILED:`, verification);
                    this.showNotification(`❌ ${theme.displayName} test FAILED`, 'error');
                }

                currentIndex++;
                setTimeout(testNextTheme, 2000); // Wait 2 seconds between tests
            }, 3000); // Wait 3 seconds for theme to apply
        };

        testNextTheme();
    }

    // Try to trigger YouTube's native theme switching mechanism
    async tryYouTubeNativeThemeSwitch(theme) {
        if (!this.webview) return;

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;

            const jsCode = `
                (function() {
                    try {
                        console.log('🔄 Attempting YouTube native theme switch to: ${theme}');

                        const isDark = '${theme}' === 'dark';
                        const results = {
                            attempts: {},
                            success: false
                        };

                        // Method 1: Try to find and click YouTube's theme toggle button
                        try {
                            const themeButtons = [
                                'button[aria-label*="theme"]',
                                'button[aria-label*="Dark"]',
                                'button[aria-label*="Light"]',
                                'button[aria-label*="Appearance"]',
                                '[role="button"][aria-label*="theme"]',
                                '.ytd-topbar-menu-button-renderer button',
                                'ytd-toggle-theme-compact-link-renderer'
                            ];

                            let buttonFound = false;
                            for (const selector of themeButtons) {
                                const buttons = document.querySelectorAll(selector);
                                if (buttons.length > 0) {
                                    console.log(\`Found potential theme button: \${selector}\`);
                                    buttons[0].click();
                                    buttonFound = true;
                                    break;
                                }
                            }

                            results.attempts.themeButton = buttonFound ? 'clicked' : 'not found';
                        } catch (e) {
                            results.attempts.themeButton = 'error: ' + e.message;
                        }

                        // Method 2: Try to access YouTube's settings menu
                        try {
                            const settingsSelectors = [
                                'button[aria-label*="Settings"]',
                                'button[aria-label*="settings"]',
                                '.ytp-settings-button',
                                'ytd-topbar-menu-button-renderer button'
                            ];

                            let settingsFound = false;
                            for (const selector of settingsSelectors) {
                                const buttons = document.querySelectorAll(selector);
                                if (buttons.length > 0) {
                                    console.log(\`Found settings button: \${selector}\`);
                                    buttons[0].click();
                                    settingsFound = true;

                                    // Wait a bit and look for appearance/theme options
                                    setTimeout(() => {
                                        const appearanceOptions = document.querySelectorAll('[role="menuitem"]');
                                        appearanceOptions.forEach(option => {
                                            if (option.textContent.toLowerCase().includes('appearance') ||
                                                option.textContent.toLowerCase().includes('theme')) {
                                                option.click();
                                            }
                                        });
                                    }, 500);

                                    break;
                                }
                            }

                            results.attempts.settingsMenu = settingsFound ? 'clicked' : 'not found';
                        } catch (e) {
                            results.attempts.settingsMenu = 'error: ' + e.message;
                        }

                        // Method 3: Try keyboard shortcuts
                        try {
                            // Some sites use keyboard shortcuts for theme switching
                            const shortcuts = [
                                { key: 't', ctrlKey: true }, // Ctrl+T
                                { key: 'd', ctrlKey: true }, // Ctrl+D
                                { key: 'l', ctrlKey: true }  // Ctrl+L
                            ];

                            shortcuts.forEach(shortcut => {
                                const event = new KeyboardEvent('keydown', {
                                    key: shortcut.key,
                                    ctrlKey: shortcut.ctrlKey,
                                    bubbles: true
                                });
                                document.dispatchEvent(event);
                            });

                            results.attempts.keyboardShortcuts = 'attempted';
                        } catch (e) {
                            results.attempts.keyboardShortcuts = 'error: ' + e.message;
                        }

                        // Method 4: Try to manipulate YouTube's internal state directly
                        try {
                            if (window.yt && window.yt.config_) {
                                // Try to trigger YouTube's internal theme change
                                const originalTheme = window.yt.config_.EXPERIMENT_FLAGS?.web_dark_theme;
                                window.yt.config_.EXPERIMENT_FLAGS = window.yt.config_.EXPERIMENT_FLAGS || {};
                                window.yt.config_.EXPERIMENT_FLAGS.web_dark_theme = isDark;

                                // Try to trigger a re-render
                                if (window.yt.app && window.yt.app.onThemeChanged) {
                                    window.yt.app.onThemeChanged();
                                }

                                results.attempts.internalState = \`changed from \${originalTheme} to \${isDark}\`;
                            } else {
                                results.attempts.internalState = 'no yt.config found';
                            }
                        } catch (e) {
                            results.attempts.internalState = 'error: ' + e.message;
                        }

                        // Method 5: Try to find and manipulate theme-related elements
                        try {
                            const themeElements = document.querySelectorAll('[class*="theme"], [class*="dark"], [class*="light"]');
                            if (themeElements.length > 0) {
                                themeElements.forEach(el => {
                                    if (isDark) {
                                        el.classList.add('dark');
                                        el.classList.remove('light');
                                    } else {
                                        el.classList.add('light');
                                        el.classList.remove('dark');
                                    }
                                });
                                results.attempts.themeElements = \`modified \${themeElements.length} elements\`;
                            } else {
                                results.attempts.themeElements = 'no theme elements found';
                            }
                        } catch (e) {
                            results.attempts.themeElements = 'error: ' + e.message;
                        }

                        console.log('🔄 YouTube native theme switch results:', results);
                        return results;
                    } catch (error) {
                        console.error('❌ Error in YouTube native theme switch:', error);
                        return { success: false, error: error.message };
                    }
                })();
            `;

            const result = await webContents.executeJavaScript(jsCode);
            console.log('🔄 YouTube native theme switch result:', result);
            return result;

        } catch (error) {
            console.error('Error in YouTube native theme switch attempt:', error);
            return null;
        }
    }

    // Comprehensive YouTube theme investigation
    async inspectYouTubeThemeStructure() {
        if (!this.webview) return;

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;

            const jsCode = `
                (function() {
                    try {
                        console.log('🔍 Deep YouTube theme investigation...');

                        const result = {
                            pageInfo: {
                                url: window.location.href,
                                title: document.title,
                                isYouTube: window.location.hostname.includes('youtube.com')
                            },
                            htmlElement: {},
                            bodyElement: {},
                            ytdAppElement: {},
                            themeElements: {},
                            localStorage: {},
                            sessionStorage: {},
                            ytConfig: {},
                            cssVariables: {},
                            themeClasses: []
                        };

                        // Analyze HTML element
                        const html = document.documentElement;
                        result.htmlElement = {
                            tagName: html.tagName,
                            className: html.className,
                            attributes: {},
                            computedStyles: {}
                        };

                        for (let attr of html.attributes) {
                            result.htmlElement.attributes[attr.name] = attr.value;
                        }

                        const htmlStyles = window.getComputedStyle(html);
                        result.htmlElement.computedStyles = {
                            backgroundColor: htmlStyles.backgroundColor,
                            color: htmlStyles.color,
                            colorScheme: htmlStyles.colorScheme
                        };

                        // Analyze body element
                        const body = document.body;
                        if (body) {
                            result.bodyElement = {
                                className: body.className,
                                attributes: {},
                                computedStyles: {}
                            };

                            for (let attr of body.attributes) {
                                result.bodyElement.attributes[attr.name] = attr.value;
                            }

                            const bodyStyles = window.getComputedStyle(body);
                            result.bodyElement.computedStyles = {
                                backgroundColor: bodyStyles.backgroundColor,
                                color: bodyStyles.color,
                                colorScheme: bodyStyles.colorScheme
                            };
                        }

                        // Analyze ytd-app element (YouTube's main app container)
                        const ytdApp = document.querySelector('ytd-app');
                        if (ytdApp) {
                            result.ytdAppElement = {
                                tagName: ytdApp.tagName,
                                className: ytdApp.className,
                                attributes: {},
                                computedStyles: {},
                                cssVariables: {}
                            };

                            for (let attr of ytdApp.attributes) {
                                result.ytdAppElement.attributes[attr.name] = attr.value;
                            }

                            const ytdStyles = window.getComputedStyle(ytdApp);
                            result.ytdAppElement.computedStyles = {
                                backgroundColor: ytdStyles.backgroundColor,
                                color: ytdStyles.color,
                                colorScheme: ytdStyles.colorScheme
                            };

                            // Extract YouTube CSS variables
                            const ytVariables = [
                                '--yt-spec-base-background',
                                '--yt-spec-raised-background',
                                '--yt-spec-text-primary',
                                '--yt-spec-text-secondary',
                                '--yt-spec-general-background-a',
                                '--yt-spec-general-background-b',
                                '--yt-spec-wordmark-text'
                            ];

                            ytVariables.forEach(varName => {
                                result.ytdAppElement.cssVariables[varName] = ytdStyles.getPropertyValue(varName);
                            });
                        }

                        // Check for theme-related elements
                        const themeSelectors = [
                            'ytd-masthead',
                            'ytd-guide-renderer',
                            'ytd-page-manager',
                            '[dark]',
                            '[data-theme]',
                            '.dark',
                            '.light'
                        ];

                        themeSelectors.forEach(selector => {
                            try {
                                const elements = document.querySelectorAll(selector);
                                if (elements.length > 0) {
                                    result.themeElements[selector] = {
                                        count: elements.length,
                                        firstElement: {
                                            className: elements[0].className,
                                            attributes: {}
                                        }
                                    };

                                    for (let attr of elements[0].attributes) {
                                        result.themeElements[selector].firstElement.attributes[attr.name] = attr.value;
                                    }
                                }
                            } catch (e) {
                                result.themeElements[selector] = { error: e.message };
                            }
                        });

                        // Comprehensive localStorage analysis
                        try {
                            for (let i = 0; i < localStorage.length; i++) {
                                const key = localStorage.key(i);
                                if (key) {
                                    const value = localStorage.getItem(key);
                                    result.localStorage[key] = value;
                                }
                            }
                        } catch (e) {
                            result.localStorage.error = e.message;
                        }

                        // Check sessionStorage
                        try {
                            for (let i = 0; i < sessionStorage.length; i++) {
                                const key = sessionStorage.key(i);
                                if (key && (key.includes('theme') || key.includes('dark') || key.includes('yt'))) {
                                    result.sessionStorage[key] = sessionStorage.getItem(key);
                                }
                            }
                        } catch (e) {
                            result.sessionStorage.error = e.message;
                        }

                        // Comprehensive YouTube config analysis
                        if (window.yt) {
                            result.ytConfig = {
                                hasYt: true,
                                hasConfig: !!window.yt.config_,
                                configKeys: window.yt.config_ ? Object.keys(window.yt.config_) : [],
                                experimentFlags: window.yt.config_?.EXPERIMENT_FLAGS || {},
                                hasInitialData: !!window.ytInitialData
                            };
                        }

                        // Look for theme-related CSS classes in the document
                        const allElements = document.querySelectorAll('*');
                        const themeClassPatterns = ['dark', 'light', 'theme', 'yt-spec'];
                        const foundClasses = new Set();

                        allElements.forEach(el => {
                            if (el.className && typeof el.className === 'string') {
                                el.className.split(' ').forEach(cls => {
                                    if (themeClassPatterns.some(pattern => cls.includes(pattern))) {
                                        foundClasses.add(cls);
                                    }
                                });
                            }
                        });

                        result.themeClasses = Array.from(foundClasses);

                        console.log('🔍 Comprehensive YouTube analysis result:', result);
                        return result;
                    } catch (error) {
                        console.error('❌ Error in YouTube analysis:', error);
                        return { error: error.message, stack: error.stack };
                    }
                })();
            `;

            const result = await webContents.executeJavaScript(jsCode);
            console.log('🔍 YouTube Deep Analysis:', result);
            return result;

        } catch (error) {
            console.error('Error in YouTube theme investigation:', error);
            return null;
        }
    }

    // Inspect YouTube theme after applying changes
    async inspectYouTubeThemeAfterChange(expectedTheme) {
        if (!this.webview) return;

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;

            const jsCode = `
                (function() {
                    try {
                        console.log('🔍 Inspecting YouTube after theme change to: ${expectedTheme}');

                        const result = {
                            expectedTheme: '${expectedTheme}',
                            actualState: {}
                        };

                        // Check current state
                        const html = document.documentElement;
                        result.actualState.hasDarkAttribute = html.hasAttribute('dark');
                        result.actualState.dataTheme = html.getAttribute('data-theme');
                        result.actualState.className = html.className;

                        // Check ytd-app
                        const ytdApp = document.querySelector('ytd-app');
                        if (ytdApp) {
                            const styles = window.getComputedStyle(ytdApp);
                            result.actualState.backgroundColor = styles.backgroundColor;
                            result.actualState.color = styles.color;
                        }

                        // Check if theme actually changed
                        const isDarkExpected = '${expectedTheme}' === 'dark';
                        const isDarkActual = html.hasAttribute('dark') || html.getAttribute('data-theme') === 'dark';

                        result.themeMatches = isDarkExpected === isDarkActual;

                        console.log('🔍 Theme change inspection:', result);
                        return result;
                    } catch (error) {
                        console.error('❌ Error inspecting theme change:', error);
                        return { error: error.message };
                    }
                })();
            `;

            const result = await webContents.executeJavaScript(jsCode);
            console.log(`🔍 Theme Change Result for ${expectedTheme}:`, result);

            if (!result.themeMatches) {
                console.warn(`⚠️ Theme mismatch! Expected: ${expectedTheme}, Actual state:`, result.actualState);
            }

        } catch (error) {
            console.error('Error inspecting theme change:', error);
        }
    }

    // Enhanced event handlers for i18n and theme systems
    onLanguageChanged(data) {
        console.log(`🌍 Language changed: ${data.oldLanguage} -> ${data.newLanguage}`);

        // Update document direction and language
        document.documentElement.setAttribute('dir', data.meta.direction);
        document.documentElement.setAttribute('lang', data.meta.language);
        document.body.setAttribute('dir', data.meta.direction);

        // Update all translations
        this.updateAllTranslations();

        // Update webview URL if needed
        this.updateInitialWebviewLanguage(data.newLanguage);
    }

    onThemeChanged(data) {
        console.log(`🎨 Theme changed: ${data.oldTheme} -> ${data.newTheme} (effective: ${data.effectiveTheme})`);

        // Update theme toggle icon
        this.updateThemeToggleIcon();

        // Apply theme to webview
        this.applyThemeToWebview(data.effectiveTheme);
    }

    onSystemThemeDetected(data) {
        console.log(`🎨 System theme detected: ${data.theme}`);
        this.showNotification(this.t('notifications.deviceThemeDetected', { theme: data.theme }), 'info');
    }

    onSystemThemeChanged(data) {
        console.log(`🎨 System theme changed: ${data.oldTheme} -> ${data.newTheme}`);

        if (data.autoThemeActive) {
            this.showNotification(this.t('notifications.deviceThemeDetected', { theme: data.newTheme }), 'info');
        }
    }

    onThemeChangeStart(data) {
        console.log(`🎨 Theme change starting: ${data.oldTheme} -> ${data.newTheme}`);
        this.setLoadingState('theme', true);
    }

    onThemeChangeError(data) {
        console.error('🎨 Theme change error:', data.error);
        this.setLoadingState('theme', false);
        this.showNotification(this.t('errors.themeApply'), 'error');
    }

    // Translation helper method with fallback
    t(key, params = {}) {
        if (this.enhancedSystemsLoaded && this.i18n) {
            return this.i18n.t(key, params);
        } else {
            // Fallback to legacy translation system
            const currentLang = this.currentSettings.language || 'en';
            const translation = this.translations[currentLang] && this.translations[currentLang][key];
            return translation || key;
        }
    }

    // Update all translations in the UI with robust fallback
    updateAllTranslations() {
        try {
            if (this.enhancedSystemsLoaded && this.i18n) {
                // Use enhanced translation system
                this.updateElementText('title', 'backBtn', 'header.goBack');
                this.updateElementText('title', 'forwardBtn', 'header.goForward');
                this.updateElementText('title', 'reloadBtn', 'header.reload');
                this.updateElementText('title', 'settingsBtn', 'header.settings');
                this.updateElementText('title', 'fullscreenBtn', 'header.toggleFullscreen');

                // Update input placeholder
                this.updateElementText('placeholder', 'urlInput', 'header.urlPlaceholder');

                // Update settings modal
                this.updateElementText('textContent', '.modal-header h2', 'settings.title');

                // Update section headers
                const sections = document.querySelectorAll('.settings-section h3');
                if (sections[0]) sections[0].textContent = this.t('settings.videoQuality');
                if (sections[1]) sections[1].textContent = this.t('settings.playerSettings');
                if (sections[2]) sections[2].textContent = this.t('settings.appearance');

                // Update buttons
                this.updateElementText('textContent', 'saveSettingsBtn', 'settings.save');
                this.updateElementText('textContent', 'cancelSettingsBtn', 'settings.cancel');

                // Update loading indicator
                this.updateElementText('textContent', '#loadingIndicator span', 'loading.default');
            } else {
                // Use legacy translation system
                const currentLang = this.currentSettings.language || 'en';
                this.applyTranslations(currentLang);
            }

            // Update theme toggle icon with new title
            this.updateThemeToggleIcon();

        } catch (error) {
            console.error('Error updating translations:', error);
        }
    }

    // Helper method to update element text with robust fallback
    updateElementText(property, selector, translationKey) {
        try {
            const element = typeof selector === 'string' ?
                (selector.startsWith('.') || selector.startsWith('#') ?
                    document.querySelector(selector) :
                    document.getElementById(selector)) :
                selector;

            if (element) {
                const text = this.t(translationKey);
                if (property === 'textContent') {
                    element.textContent = text;
                } else if (property === 'title') {
                    element.setAttribute('title', text);
                } else if (property === 'placeholder') {
                    element.setAttribute('placeholder', text);
                }
            }
        } catch (error) {
            console.warn(`Error updating element text for ${selector}:`, error);
        }
    }

    openSettings() {
        this.isSettingsOpen = true;
        this.settingsModal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // Focus first input for accessibility
        setTimeout(() => {
            const firstInput = this.settingsModal.querySelector('input, select');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    closeSettings() {
        this.isSettingsOpen = false;
        this.settingsModal.classList.remove('show');
        document.body.style.overflow = '';

        // Reset form to current settings
        this.updateSettingsUI();
    }

    async saveSettings() {
        try {
            const newSettings = {
                videoQuality: this.currentQuality,
                autoplay: this.autoplayInput.checked,
                volume: parseInt(this.volumeInput.value),
                theme: this.themeSelect.value,
                language: this.languageSelect.value
            };
            
            const result = await window.electronAPI.saveSettings(newSettings);
            
            if (result.success) {
                const oldSettings = this.currentSettings;
                this.currentSettings = newSettings;


                // Apply theme if changed with robust handling
                if (newSettings.theme !== oldSettings.theme) {
                    if (this.enhancedSystemsLoaded && this.themeManager) {
                        await this.themeManager.applyTheme(newSettings.theme, false);
                    } else {
                        this.applyThemeLegacy(newSettings.theme);
                    }
                }

                // Apply language if changed with robust handling
                if (newSettings.language !== oldSettings.language) {
                    if (this.enhancedSystemsLoaded && this.i18n) {
                        await this.i18n.changeLanguage(newSettings.language);
                    } else {
                        this.applyLanguageLegacy(newSettings.language);
                    }
                }

                // Apply quality to YouTube if changed
                if (newSettings.videoQuality !== oldSettings.videoQuality) {
                    await this.setVideoQuality(newSettings.videoQuality);
                    // Apply immediately to current video
                    setTimeout(() => {
                        this.applyQualityToYouTube();
                    }, 500);
                }

                this.closeSettings();

                // Show success message with enhanced translation support
                let message;
                if (this.enhancedSystemsLoaded && this.i18n) {
                    message = this.t('notifications.settingsSaved');
                } else {
                    message = newSettings.language === 'ar' ? 'تم حفظ الإعدادات بنجاح!' : 'Settings saved successfully!';
                }
                this.showNotification(message, 'success');
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showNotification('Failed to save settings', 'error');
        }
    }

    async setVideoQuality(quality) {
        try {
            const result = await window.electronAPI.setVideoQuality(quality);

            if (result.success) {
                this.currentQuality = quality;
                this.updateQualityDisplay();

                // Show feedback to user
                this.showNotification(`Applying ${quality} quality...`, 'info');

                // Apply quality with retry mechanism
                this.applyQualityToYouTube();

                // Show success message after a delay
                setTimeout(() => {
                    this.showNotification(`Quality set to ${quality}`, 'success');
                }, 2000);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('Failed to set video quality:', error);
            this.showNotification('Failed to set video quality', 'error');
        }
    }

    applyQualityToYouTube() {
        // Inject JavaScript to change YouTube video quality using enhanced DOM manipulation
        const qualityScript = `
            (function() {
                try {
                    console.log('Applying quality setting: ${this.currentQuality}');
                    
                    function waitForElement(selector, timeout = 10000) {
                        return new Promise((resolve, reject) => {
                            const element = document.querySelector(selector);
                            if (element) {
                                resolve(element);
                                return;
                            }
                            
                            const observer = new MutationObserver((mutations) => {
                                const element = document.querySelector(selector);
                                if (element) {
                                    observer.disconnect();
                                    resolve(element);
                                }
                            });
                            
                            observer.observe(document.body, {
                                childList: true,
                                subtree: true
                            });
                            
                            setTimeout(() => {
                                observer.disconnect();
                                reject(new Error('Element not found: ' + selector));
                            }, timeout);
                        });
                    }
                    
                    async function applyQualitySetting() {
                        try {
                            // Wait for video player to be ready
                            const player = await waitForElement('#movie_player');
                            console.log('Player found');
                            
                            // Wait for video to be loaded
                            const video = player.querySelector('video');
                            if (!video || video.readyState < 2) {
                                console.log('Video not ready, waiting...');
                                setTimeout(applyQualitySetting, 2000);
                                return;
                            }
                            
                            // Click settings button
                            const settingsButton = await waitForElement('.ytp-settings-button');
                            console.log('Settings button found');
                            settingsButton.click();
                            
                            // Wait for settings menu to appear
                            await new Promise(resolve => setTimeout(resolve, 300));
                            
                            // Look for quality menu item
                            const menuItems = document.querySelectorAll('.ytp-menuitem');
                            let qualityMenuItem = null;
                            
                            for (let item of menuItems) {
                                const text = item.textContent.toLowerCase();
                                if (text.includes('quality') || text.includes('144p') || text.includes('240p') || 
                                    text.includes('360p') || text.includes('480p') || text.includes('720p') || 
                                    text.includes('1080p') || text.includes('4k')) {
                                    qualityMenuItem = item;
                                    break;
                                }
                            }
                            
                            if (!qualityMenuItem) {
                                console.log('Quality menu item not found, trying alternative selectors');
                                // Try alternative selectors
                                const altSelectors = [
                                    '[aria-label*="quality"]',
                                    '[aria-label*="Quality"]',
                                    '.ytp-settings-menu .ytp-menuitem',
                                    '.ytp-panel-menu .ytp-menuitem'
                                ];
                                
                                for (let selector of altSelectors) {
                                    const items = document.querySelectorAll(selector);
                                    for (let item of items) {
                                        const text = item.textContent.toLowerCase();
                                        if (text.includes('quality') || text.includes('144p') || text.includes('240p') || 
                                            text.includes('360p') || text.includes('480p') || text.includes('720p') || 
                                            text.includes('1080p') || text.includes('4k')) {
                                            qualityMenuItem = item;
                                            break;
                                        }
                                    }
                                    if (qualityMenuItem) break;
                                }
                            }
                            
                            if (qualityMenuItem) {
                                console.log('Quality menu item found, clicking...');
                                qualityMenuItem.click();
                                
                                // Wait for quality submenu to appear
                                await new Promise(resolve => setTimeout(resolve, 300));
                                
                                // Find and click the target quality
                                const targetQuality = '${this.currentQuality}';
                                const qualityOptions = document.querySelectorAll('.ytp-menuitem');
                                let qualityFound = false;
                                
                                // Enhanced quality mapping
                                const qualityMap = {
                                    '144p': ['144p', 'tiny', '144'],
                                    '240p': ['240p', 'small', '240'],
                                    '360p': ['360p', 'medium', '360'],
                                    '480p': ['480p', 'large', '480'],
                                    '720p': ['720p', 'hd720', '720', 'hd'],
                                    '1080p': ['1080p', 'hd1080', '1080', 'full hd'],
                                    '4k': ['4k', 'hd1440', '2160p', '2160', 'ultra hd', 'uhd']
                                };
                                
                                const targetQualities = qualityMap[targetQuality] || [targetQuality];
                                
                                for (let option of qualityOptions) {
                                    const text = option.textContent.toLowerCase();
                                    console.log('Checking quality option:', text);
                                    
                                    for (let quality of targetQualities) {
                                        if (text.includes(quality.toLowerCase())) {
                                            console.log('Found matching quality option:', text);
                                            option.click();
                                            qualityFound = true;
                                            break;
                                        }
                                    }
                                    
                                    if (qualityFound) break;
                                }
                                
                                if (!qualityFound) {
                                    console.log('Target quality not found, trying auto');
                                    // Try to find auto quality as fallback
                                    for (let option of qualityOptions) {
                                        const text = option.textContent.toLowerCase();
                                        if (text.includes('auto') || text.includes('automatic')) {
                                            console.log('Found auto quality option');
                                            option.click();
                                            break;
                                        }
                                    }
                                }
                            } else {
                                console.log('Quality menu item not found');
                            }
                            
                            // Close settings menu
                            setTimeout(() => {
                                const closeButton = document.querySelector('.ytp-settings-button');
                                if (closeButton) {
                                    closeButton.click();
                                }
                            }, 500);
                            
                        } catch (error) {
                            console.error('Error in applyQualitySetting:', error);
                            // Retry after a delay
                            setTimeout(applyQualitySetting, 3000);
                        }
                    }
                    
                    // Try multiple methods to apply quality
                    async function tryAllQualityMethods() {
                        // Method 1: Enhanced DOM manipulation
                        try {
                            await applyQualitySetting();
                        } catch (error) {
                            console.log('DOM method failed, trying alternative methods');
                            
                            // Method 2: Try to use YouTube's internal API if available
                            try {
                                const player = document.querySelector('#movie_player');
                                if (player && player.getPlayerState) {
                                    const targetQuality = '${this.currentQuality}';
                                    console.log('Trying YouTube internal API for quality:', targetQuality);
                                    
                                    // Map quality to YouTube's internal format
                                    const qualityMap = {
                                        '144p': 'tiny',
                                        '240p': 'small', 
                                        '360p': 'medium',
                                        '480p': 'large',
                                        '720p': 'hd720',
                                        '1080p': 'hd1080',
                                        '4k': 'hd1440'
                                    };
                                    
                                    const youtubeQuality = qualityMap[targetQuality] || targetQuality;
                                    
                                    // Try to set quality via internal API
                                    if (player.setPlaybackQualityRange) {
                                        player.setPlaybackQualityRange(youtubeQuality, youtubeQuality);
                                        console.log('Quality set via internal API');
                                    }
                                }
                            } catch (apiError) {
                                console.log('Internal API method failed:', apiError);
                                
                                // Method 3: Try keyboard shortcuts
                                try {
                                    const targetQuality = '${this.currentQuality}';
                                    console.log('Trying keyboard shortcut method');
                                    
                                    // Press 'i' to open quality menu (YouTube shortcut)
                                    const keyEvent = new KeyboardEvent('keydown', {
                                        key: 'i',
                                        code: 'KeyI',
                                        keyCode: 73,
                                        which: 73,
                                        bubbles: true,
                                        cancelable: true
                                    });
                                    
                                    document.dispatchEvent(keyEvent);
                                    
                                    // Wait and try to select quality
                                    setTimeout(() => {
                                        const qualityOptions = document.querySelectorAll('.ytp-menuitem');
                                        const targetQualities = ['144p', '240p', '360p', '480p', '720p', '1080p', '4k'];
                                        
                                        for (let option of qualityOptions) {
                                            const text = option.textContent.toLowerCase();
                                            if (text.includes(targetQuality.toLowerCase())) {
                                                option.click();
                                                console.log('Quality selected via keyboard shortcut');
                                                break;
                                            }
                                        }
                                    }, 500);
                                    
                                } catch (keyboardError) {
                                    console.log('Keyboard shortcut method failed:', keyboardError);
                                }
                            }
                        }
                    }
                    
                    // Start the quality application process
                    tryAllQualityMethods();
                    
                } catch (error) {
                    console.error('Failed to apply quality to YouTube:', error);
                }
            })();
        `;
        
        this.webview.executeJavaScript(qualityScript);
    }

    openQualityOverlay() {
        console.log('Opening quality overlay');
        this.isQualityOverlayOpen = true;
        this.qualityOverlay.classList.add('show');
        this.updateQualityDisplay();
    }

    closeQualityOverlay() {
        console.log('Closing quality overlay');
        this.isQualityOverlayOpen = false;
        this.qualityOverlay.classList.remove('show');
    }

    // Navigation methods
    navigate() {
        let url = this.urlInput.value.trim();

        if (!url) return;

        // Handle YouTube URLs and search
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            if (url.includes('youtube.com') || url.includes('youtu.be')) {
                url = 'https://' + url;
            } else {
                // Treat as YouTube search
                url = `https://www.youtube.com/results?search_query=${encodeURIComponent(url)}`;
            }
        }

        // Add language parameter to YouTube URLs
        if (url.includes('youtube.com')) {
            const currentLanguage = this.currentSettings.language || 'ar';
            url = this.addLanguageParameterToUrl(url, currentLanguage);
        }

        this.webview.loadURL(url);
    }

    // Load test theme page for debugging
    loadThemeTestPage() {
        const testPagePath = `file://${__dirname}/../test-theme.html`;
        console.log('🧪 Loading theme test page:', testPagePath);
        this.webview.loadURL(testPagePath);
        this.urlInput.value = testPagePath;
    }

    // Force load YouTube for direct testing
    forceLoadYouTube() {
        console.log('🎯 Force loading YouTube for direct theme testing');
        this.showNotification('Loading YouTube for direct theme testing...', 'info');

        // Load YouTube with minimal parameters to avoid issues
        const youtubeUrl = 'https://www.youtube.com';
        this.webview.loadURL(youtubeUrl);
        this.urlInput.value = youtubeUrl;

        // Start theme testing after YouTube loads
        setTimeout(() => {
            this.testDirectYouTubeThemes();
        }, 5000);
    }

    // Run automatic theme testing
    async runAutomaticThemeTest() {
        console.log('🚀 Starting automatic theme test...');
        this.showNotification('Starting automatic theme test...', 'info');

        const themes = [
            { name: 'dark', displayName: 'Dark Mode' },
            { name: 'light', displayName: 'Light Mode' },
            { name: 'auto', displayName: 'Auto Mode' }
        ];

        for (let i = 0; i < themes.length; i++) {
            const theme = themes[i];
            console.log(`🧪 Testing ${theme.displayName}...`);
            this.showNotification(`Testing ${theme.displayName}...`, 'info');

            // Apply theme using the app's theme system
            if (this.enhancedSystemsLoaded && this.themeManager) {
                await this.themeManager.applyTheme(theme.name, false);
            } else {
                this.applyThemeLegacy(theme.name);
            }

            // Apply to webview
            const effectiveTheme = this.getEffectiveTheme(theme.name);
            this.applyThemeToWebview(effectiveTheme);
            await this.injectYouTubeNativeTheme(effectiveTheme);

            // Wait for theme to apply
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Verify theme application
            const verification = await this.verifyThemeApplication(effectiveTheme);

            if (verification.success) {
                console.log(`✅ ${theme.displayName} test PASSED`);
                this.showNotification(`✅ ${theme.displayName} test PASSED`, 'success');
            } else {
                console.warn(`❌ ${theme.displayName} test FAILED:`, verification);
                this.showNotification(`❌ ${theme.displayName} test FAILED`, 'error');
            }

            // Wait before next test
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.log('🎉 Automatic theme test completed!');
        this.showNotification('🎉 Automatic theme test completed!', 'success');
    }

    goBack() {
        if (this.webview.canGoBack()) {
            this.webview.goBack();
        }
    }

    goForward() {
        if (this.webview.canGoForward()) {
            this.webview.goForward();
        }
    }

    reload() {
        this.webview.reload();
    }

    toggleFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            document.documentElement.requestFullscreen();
        }
    }

    updateUrlBar(url) {
        this.urlInput.value = url;
    }

    // Loading indicator
    showLoading() {
        this.loadingIndicator.classList.add('show');
    }

    hideLoading() {
        this.loadingIndicator.classList.remove('show');
    }

    // Keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + , to open settings
        if ((e.ctrlKey || e.metaKey) && e.key === ',') {
            e.preventDefault();
            this.openSettings();
        }
        
        // Ctrl/Cmd + R to reload
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            this.reload();
        }
        
        // Ctrl/Cmd + L to focus URL bar
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            this.urlInput.focus();
            this.urlInput.select();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            if (this.isSettingsOpen) {
                this.closeSettings();
            } else if (this.isQualityOverlayOpen) {
                this.closeQualityOverlay();
            }
        }
        
        // Ctrl/Cmd + T to toggle theme
        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
            e.preventDefault();
            this.toggleTheme();
        }
    }

    // Set up quality monitoring to apply settings to new videos
    setupQualityMonitoring() {
        const monitoringScript = `
            (function() {
                let lastUrl = '';
                let qualityApplied = false;
                let retryCount = 0;
                const maxRetries = 5;
                
                // Monitor for URL changes (new videos)
                function checkForVideoChanges() {
                    const currentUrl = window.location.href;
                    if (currentUrl !== lastUrl && currentUrl.includes('/watch')) {
                        console.log('New video detected:', currentUrl);
                        lastUrl = currentUrl;
                        qualityApplied = false;
                        retryCount = 0;
                        
                        // Wait for video to load and apply quality
                        setTimeout(() => {
                            if (!qualityApplied) {
                                console.log('Applying quality to new video');
                                window.postMessage({ type: 'APPLY_QUALITY' }, '*');
                                qualityApplied = true;
                            }
                        }, 3000);
                    }
                }
                
                // Monitor for video player state changes
                function monitorVideoPlayer() {
                    const player = document.querySelector('#movie_player');
                    if (player) {
                        // Check if video is playing
                        const video = player.querySelector('video');
                        if (video && !video.paused && video.currentTime > 0) {
                            if (!qualityApplied && retryCount < maxRetries) {
                                console.log('Video is playing, applying quality (attempt ' + (retryCount + 1) + ')');
                                setTimeout(() => {
                                    window.postMessage({ type: 'APPLY_QUALITY' }, '*');
                                    retryCount++;
                                    if (retryCount >= maxRetries) {
                                        qualityApplied = true;
                                    }
                                }, 1000);
                            }
                        }
                    }
                }
                
                // Monitor for settings button availability
                function monitorSettingsButton() {
                    const settingsButton = document.querySelector('.ytp-settings-button');
                    if (settingsButton && !qualityApplied && retryCount < maxRetries) {
                        console.log('Settings button available, applying quality');
                        setTimeout(() => {
                            window.postMessage({ type: 'APPLY_QUALITY' }, '*');
                            retryCount++;
                            if (retryCount >= maxRetries) {
                                qualityApplied = true;
                            }
                        }, 500);
                    }
                }
                
                // Set up monitoring with different intervals
                setInterval(checkForVideoChanges, 1000);
                setInterval(monitorVideoPlayer, 2000);
                setInterval(monitorSettingsButton, 1500);
                
                console.log('Enhanced quality monitoring set up');
            })();
        `;
        
        this.webview.executeJavaScript(monitoringScript);
        
        // Listen for quality application messages from the webview
        this.webview.addEventListener('ipc-message', (event) => {
            if (event.channel.type === 'APPLY_QUALITY') {
                console.log('Received quality application request from webview');
                this.applyQualityToYouTube();
            }
        });
    }
    
    // Test webview functionality
    testWebviewFunctionality() {
        try {
            console.log('Testing webview functionality...');
            
            // Check if webview element exists
            if (!this.webview) {
                console.error('Webview element not found');
                this.showNotification('Webview element not found', 'error');
                return;
            }
            
            // Check if webview is attached
            if (!this.webview.getWebContentsId) {
                console.error('Webview not properly attached');
                this.showNotification('Webview not properly attached', 'error');
                return;
            }
            
            console.log('Webview functionality test passed');
            this.showNotification('YouTube player loaded successfully', 'success');
            
        } catch (error) {
            console.error('Webview functionality test failed:', error);
            this.showNotification('Webview functionality test failed', 'error');
        }
    }
    
    // Notification system
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Get theme-aware colors
        const colors = {
            error: getComputedStyle(document.body).getPropertyValue('--error-color').trim() || '#ff4444',
            success: getComputedStyle(document.body).getPropertyValue('--success-color').trim() || '#44ff44',
            info: getComputedStyle(document.body).getPropertyValue('--info-color').trim() || '#4444ff'
        };
        
        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: var(--text-primary);
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 4px 12px var(--shadow-color);
            animation: slideIn 0.3s ease-out;
            border: 1px solid var(--border-color);
        `;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    


}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Add CSS animations for notifications
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // Create and initialize the player
    window.youtubePlayer = new YouTubePlayer();
});

// Handle any unhandled errors
window.addEventListener('error', (e) => {
    console.error('Unhandled error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
});

