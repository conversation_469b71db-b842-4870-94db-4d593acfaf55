// Internationalization (i18n) system for YouTube Player App
// Following YouTube's resource loading pattern with dynamic imports

import { en } from './en.js';
import { ar } from './ar.js';

class I18nManager {
    constructor() {
        this.currentLanguage = 'en';
        this.currentResources = en;
        this.fallbackLanguage = 'en';
        this.fallbackResources = en;
        this.loadedLanguages = new Map();
        this.observers = new Set();
        
        // Pre-load available languages
        this.loadedLanguages.set('en', en);
        this.loadedLanguages.set('ar', ar);
        
        // Detect system language
        this.detectSystemLanguage();
    }

    // Detect system language following YouTube's approach
    detectSystemLanguage() {
        try {
            // Get browser/system language
            const systemLang = navigator.language || navigator.userLanguage || 'en';
            const langCode = systemLang.split('-')[0].toLowerCase();
            
            // Check if we support this language
            if (this.loadedLanguages.has(langCode)) {
                this.currentLanguage = langCode;
                this.currentResources = this.loadedLanguages.get(langCode);
            }
            
            console.log(`🌍 System language detected: ${systemLang}, using: ${this.currentLanguage}`);
        } catch (error) {
            console.warn('Failed to detect system language, using fallback:', error);
            this.currentLanguage = this.fallbackLanguage;
            this.currentResources = this.fallbackResources;
        }
    }

    // Get current language metadata
    getCurrentLanguageMeta() {
        return this.currentResources.meta;
    }

    // Check if language is RTL
    isRTL(language = this.currentLanguage) {
        const resources = this.loadedLanguages.get(language);
        return resources?.meta?.direction === 'rtl';
    }

    // Get available languages
    getAvailableLanguages() {
        return Array.from(this.loadedLanguages.keys()).map(lang => {
            const resources = this.loadedLanguages.get(lang);
            return {
                code: lang,
                name: resources.meta.name,
                direction: resources.meta.direction,
                locale: resources.meta.locale
            };
        });
    }

    // Change language with loading state
    async changeLanguage(languageCode) {
        if (!this.loadedLanguages.has(languageCode)) {
            throw new Error(`Language ${languageCode} is not available`);
        }

        const oldLanguage = this.currentLanguage;
        this.currentLanguage = languageCode;
        this.currentResources = this.loadedLanguages.get(languageCode);

        // Notify observers
        this.notifyObservers('languageChanged', {
            oldLanguage,
            newLanguage: languageCode,
            isRTL: this.isRTL(),
            meta: this.getCurrentLanguageMeta()
        });

        console.log(`🌍 Language changed from ${oldLanguage} to ${languageCode}`);
        return true;
    }

    // Get translated text with interpolation
    t(key, params = {}) {
        try {
            const keys = key.split('.');
            let value = this.currentResources;

            // Navigate through nested keys
            for (const k of keys) {
                if (value && typeof value === 'object' && k in value) {
                    value = value[k];
                } else {
                    // Fallback to English if key not found
                    value = this.fallbackResources;
                    for (const fallbackKey of keys) {
                        if (value && typeof value === 'object' && fallbackKey in value) {
                            value = value[fallbackKey];
                        } else {
                            console.warn(`Translation key not found: ${key}`);
                            return key; // Return key as fallback
                        }
                    }
                    break;
                }
            }

            // If value is not a string, return the key
            if (typeof value !== 'string') {
                console.warn(`Translation value is not a string for key: ${key}`);
                return key;
            }

            // Interpolate parameters
            return this.interpolate(value, params);
        } catch (error) {
            console.error(`Error getting translation for key ${key}:`, error);
            return key;
        }
    }

    // Interpolate parameters in translation strings
    interpolate(text, params) {
        if (!params || Object.keys(params).length === 0) {
            return text;
        }

        return text.replace(/\{(\w+)\}/g, (match, key) => {
            return params.hasOwnProperty(key) ? params[key] : match;
        });
    }

    // Add observer for language changes
    addObserver(callback) {
        this.observers.add(callback);
    }

    // Remove observer
    removeObserver(callback) {
        this.observers.delete(callback);
    }

    // Notify all observers
    notifyObservers(event, data) {
        this.observers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in i18n observer:', error);
            }
        });
    }

    // Get current language code
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Get current direction
    getCurrentDirection() {
        return this.getCurrentLanguageMeta().direction;
    }

    // Get current locale
    getCurrentLocale() {
        return this.getCurrentLanguageMeta().locale;
    }

    // Format time/date according to current locale
    formatTime(date, options = {}) {
        try {
            const locale = this.getCurrentLocale();
            return new Intl.DateTimeFormat(locale, options).format(date);
        } catch (error) {
            console.warn('Error formatting time:', error);
            return date.toString();
        }
    }

    // Format numbers according to current locale
    formatNumber(number, options = {}) {
        try {
            const locale = this.getCurrentLocale();
            return new Intl.NumberFormat(locale, options).format(number);
        } catch (error) {
            console.warn('Error formatting number:', error);
            return number.toString();
        }
    }
}

// Create singleton instance
const i18n = new I18nManager();

// Export both the instance and the class
export { I18nManager, i18n };
export default i18n;
