/**
 * Theme Integration Layer
 * Seamless integration between ThemeManager and app settings
 */

class ThemeIntegration {
  constructor(options = {}) {
    this.options = {
      themeManager: null,
      appSettings: null,
      storageKey: 'youtube-app-theme-integration',
      syncInterval: 1000, // ms
      enableDebugLogging: false,
      ...options
    };

    this.themeManager = this.options.themeManager;
    this.appSettings = this.options.appSettings;
    this.syncTimer = null;
    this.isInitialized = false;
    this.lastKnownTheme = null;
    
    this.init();
  }

  /**
   * Initialize the integration
   */
  async init() {
    try {
      if (!this.themeManager) {
        throw new Error('ThemeManager instance is required');
      }

      // Validate theme manager health
      if (typeof this.themeManager.performHealthCheck === 'function') {
        const health = this.themeManager.performHealthCheck();
        if (health.issues && health.issues.length > 0) {
          console.warn('ThemeManager health issues detected:', health.issues);
        }
      }

      // Set up bidirectional sync with error handling
      try {
        this.setupThemeManagerListeners();
      } catch (error) {
        console.error('Failed to setup theme manager listeners:', error);
        this.handleInitializationError('theme-manager-listeners', error);
      }

      try {
        this.setupAppSettingsListeners();
      } catch (error) {
        console.error('Failed to setup app settings listeners:', error);
        this.handleInitializationError('app-settings-listeners', error);
      }

      // Initial sync with error handling
      try {
        await this.performInitialSync();
      } catch (error) {
        console.error('Failed to perform initial sync:', error);
        this.handleInitializationError('initial-sync', error);
      }

      // Start periodic sync (fallback) with error handling
      try {
        this.startPeriodicSync();
      } catch (error) {
        console.error('Failed to start periodic sync:', error);
        this.handleInitializationError('periodic-sync', error);
      }

      this.isInitialized = true;
      this.log('ThemeIntegration initialized successfully');

      // Schedule health check
      setTimeout(() => this.performHealthCheck(), 2000);

    } catch (error) {
      console.error('ThemeIntegration initialization failed:', error);
      this.handleCriticalError(error);
    }
  }

  /**
   * Handle initialization errors
   */
  handleInitializationError(component, error) {
    this.log(`Initialization error in ${component}: ${error.message}`);

    // Track failed components
    if (!this.failedComponents) {
      this.failedComponents = new Set();
    }
    this.failedComponents.add(component);

    // Attempt component-specific recovery
    switch (component) {
      case 'theme-manager-listeners':
        this.enableBasicThemeSync();
        break;
      case 'app-settings-listeners':
        this.enableLocalStorageSync();
        break;
      case 'initial-sync':
        this.enableManualSync();
        break;
      case 'periodic-sync':
        this.enableEventBasedSync();
        break;
    }
  }

  /**
   * Handle critical errors
   */
  handleCriticalError(error) {
    console.error('Critical ThemeIntegration error:', error);

    // Mark as failed but partially functional
    this.isInitialized = false;
    this.isCriticalError = true;

    // Enable minimal functionality
    this.enableMinimalMode();

    // Show user notification if possible
    if (typeof window !== 'undefined' && window.showNotification) {
      window.showNotification('Theme synchronization is running in limited mode', 'warning');
    }
  }

  /**
   * Enable minimal mode for critical errors
   */
  enableMinimalMode() {
    this.log('Enabling minimal integration mode');

    // Basic theme sync without advanced features
    if (this.themeManager) {
      this.themeManager.addListener((newTheme) => {
        try {
          localStorage.setItem('theme-minimal-sync', newTheme);
        } catch (error) {
          console.error('Minimal sync failed:', error);
        }
      });
    }
  }

  /**
   * Enable basic theme sync fallback
   */
  enableBasicThemeSync() {
    this.log('Enabling basic theme sync fallback');

    // Simple polling-based sync
    this.basicSyncInterval = setInterval(() => {
      try {
        if (this.themeManager && this.appSettings) {
          const managerTheme = this.themeManager.getTheme();
          const lastSynced = localStorage.getItem('last-synced-theme');

          if (managerTheme !== lastSynced) {
            this.syncToAppSettings(managerTheme);
            localStorage.setItem('last-synced-theme', managerTheme);
          }
        }
      } catch (error) {
        console.error('Basic sync error:', error);
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Enable localStorage-based sync fallback
   */
  enableLocalStorageSync() {
    this.log('Enabling localStorage sync fallback');

    // Use localStorage as intermediary
    window.addEventListener('storage', (event) => {
      if (event.key === 'theme-fallback-sync' && event.newValue) {
        try {
          const theme = event.newValue;
          if (this.themeManager && this.themeManager.getTheme() !== theme) {
            this.themeManager.setTheme(theme, { skipAppSync: true });
          }
        } catch (error) {
          console.error('localStorage sync error:', error);
        }
      }
    });
  }

  /**
   * Enable manual sync fallback
   */
  enableManualSync() {
    this.log('Enabling manual sync fallback');

    // Provide manual sync method
    window.manualThemeSync = () => {
      try {
        if (this.themeManager && this.appSettings) {
          const theme = this.themeManager.getTheme();
          this.syncToAppSettings(theme);
          return true;
        }
        return false;
      } catch (error) {
        console.error('Manual sync error:', error);
        return false;
      }
    };
  }

  /**
   * Enable event-based sync fallback
   */
  enableEventBasedSync() {
    this.log('Enabling event-based sync fallback');

    // Use DOM events for sync
    document.addEventListener('themechange', (event) => {
      try {
        const { theme } = event.detail;
        this.syncToAppSettings(theme);
      } catch (error) {
        console.error('Event-based sync error:', error);
      }
    });
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck() {
    try {
      const health = await this.checkSyncHealth();

      if (health.issues.length > 0) {
        console.warn('ThemeIntegration health issues:', health.issues);

        // Attempt automatic repair
        const repaired = await this.repairSync();
        if (repaired) {
          console.log('ThemeIntegration health issues repaired');
        } else {
          console.warn('Some ThemeIntegration health issues could not be repaired');
        }
      } else {
        console.log('✅ ThemeIntegration health check passed');
      }

      return health;
    } catch (error) {
      console.error('ThemeIntegration health check failed:', error);
      return { issues: [`Health check error: ${error.message}`] };
    }
  }

  /**
   * Set up theme manager listeners
   */
  setupThemeManagerListeners() {
    if (!this.themeManager) return;

    // Listen for theme changes from ThemeManager
    this.themeManager.addListener((newTheme, previousTheme, effectiveTheme) => {
      this.log(`ThemeManager changed: ${previousTheme} → ${newTheme} (effective: ${effectiveTheme})`);
      this.syncToAppSettings(newTheme);
    });

    // Listen for DOM events
    document.addEventListener('themechange', (event) => {
      const { theme, effectiveTheme } = event.detail;
      this.log(`DOM theme change event: ${theme} (effective: ${effectiveTheme})`);
      this.handleThemeChange(theme, effectiveTheme);
    });
  }

  /**
   * Set up app settings listeners
   */
  setupAppSettingsListeners() {
    if (!this.appSettings) {
      this.log('No app settings available - using localStorage fallback');
      this.setupLocalStorageListeners();
      return;
    }

    try {
      // Listen for app settings theme changes
      if (typeof this.appSettings.onThemeChange === 'function') {
        this.appSettings.onThemeChange((theme) => {
          this.log(`App settings changed to: ${theme}`);
          this.syncFromAppSettings(theme);
        });
      }

      // Listen for app settings ready event
      if (typeof this.appSettings.onReady === 'function') {
        this.appSettings.onReady(() => {
          this.log('App settings ready - performing sync');
          this.performInitialSync();
        });
      }

    } catch (error) {
      console.error('Failed to setup app settings listeners:', error);
      this.setupLocalStorageListeners();
    }
  }

  /**
   * Set up localStorage listeners as fallback
   */
  setupLocalStorageListeners() {
    window.addEventListener('storage', (event) => {
      if (event.key === this.options.storageKey && event.newValue) {
        try {
          const data = JSON.parse(event.newValue);
          if (data.theme && data.theme !== this.lastKnownTheme) {
            this.log(`Storage sync: ${data.theme}`);
            this.syncFromStorage(data.theme);
          }
        } catch (error) {
          console.error('Failed to parse storage data:', error);
        }
      }
    });
  }

  /**
   * Perform initial sync between systems
   */
  async performInitialSync() {
    try {
      let appTheme = null;
      let managerTheme = this.themeManager?.getTheme();

      // Get theme from app settings (async)
      if (this.appSettings?.getTheme) {
        try {
          appTheme = await this.appSettings.getTheme();
        } catch (error) {
          console.warn('Failed to get theme from app settings:', error);
        }
      }

      // Fallback to localStorage if app settings unavailable
      if (!appTheme) {
        try {
          const stored = localStorage.getItem(this.options.storageKey);
          if (stored) {
            const data = JSON.parse(stored);
            appTheme = data.theme;
          }
        } catch (error) {
          console.warn('Failed to get theme from localStorage:', error);
        }
      }

      this.log(`Initial sync - App: ${appTheme}, Manager: ${managerTheme}`);

      // Determine which theme to use (prioritize app settings)
      let targetTheme = appTheme || managerTheme || 'auto';

      // Validate theme
      if (!['light', 'dark', 'auto'].includes(targetTheme)) {
        this.log(`Invalid theme detected: ${targetTheme}, falling back to auto`);
        targetTheme = 'auto';
      }

      // Apply the theme
      if (this.themeManager && targetTheme !== managerTheme) {
        this.themeManager.setTheme(targetTheme, { skipAppSync: true });
      }

      this.lastKnownTheme = targetTheme;

      // Ensure app settings are in sync
      if (appTheme !== targetTheme && this.appSettings?.setTheme) {
        try {
          await this.appSettings.setTheme(targetTheme);
        } catch (error) {
          console.warn('Failed to sync theme to app settings:', error);
        }
      }

    } catch (error) {
      console.error('Initial sync failed:', error);
      // Fallback to auto theme
      if (this.themeManager) {
        this.themeManager.setTheme('auto', { skipAppSync: true });
      }
    }
  }

  /**
   * Sync theme to app settings
   */
  syncToAppSettings(theme) {
    if (!theme || theme === this.lastKnownTheme) return;

    try {
      // Sync to app settings if available
      if (this.appSettings?.setTheme) {
        this.appSettings.setTheme(theme);
        this.log(`Synced to app settings: ${theme}`);
      } else {
        // Fallback to localStorage
        this.syncToStorage(theme);
      }

      this.lastKnownTheme = theme;

    } catch (error) {
      console.error('Failed to sync to app settings:', error);
    }
  }

  /**
   * Sync theme from app settings
   */
  syncFromAppSettings(theme) {
    if (!theme || theme === this.lastKnownTheme) return;

    try {
      if (this.themeManager) {
        this.themeManager.setTheme(theme, { skipAppSync: true });
        this.log(`Synced from app settings: ${theme}`);
      }

      this.lastKnownTheme = theme;

    } catch (error) {
      console.error('Failed to sync from app settings:', error);
    }
  }

  /**
   * Sync to localStorage (fallback)
   */
  syncToStorage(theme) {
    try {
      const data = {
        theme,
        timestamp: Date.now(),
        source: 'theme-integration'
      };
      localStorage.setItem(this.options.storageKey, JSON.stringify(data));
      this.log(`Synced to storage: ${theme}`);
    } catch (error) {
      console.error('Failed to sync to storage:', error);
    }
  }

  /**
   * Sync from localStorage (fallback)
   */
  syncFromStorage(theme) {
    if (!theme || theme === this.lastKnownTheme) return;

    try {
      if (this.themeManager) {
        this.themeManager.setTheme(theme, { skipAppSync: true });
        this.log(`Synced from storage: ${theme}`);
      }

      this.lastKnownTheme = theme;

    } catch (error) {
      console.error('Failed to sync from storage:', error);
    }
  }

  /**
   * Handle theme change events
   */
  handleThemeChange(theme, effectiveTheme) {
    try {
      // Update UI components that need manual updates
      this.updateCustomComponents(theme, effectiveTheme);
      
      // Notify external systems
      this.notifyExternalSystems(theme, effectiveTheme);
      
    } catch (error) {
      console.error('Failed to handle theme change:', error);
    }
  }

  /**
   * Update custom components that don't use CSS variables
   */
  updateCustomComponents(theme, effectiveTheme) {
    try {
      // Update webview theme
      this.updateWebviewTheme(effectiveTheme);
      
      // Update settings modal theme selector
      this.updateSettingsThemeSelector(theme);
      
    } catch (error) {
      console.error('Failed to update custom components:', error);
    }
  }

  /**
   * Update webview theme
   */
  updateWebviewTheme(effectiveTheme) {
    try {
      const webview = document.getElementById('webview');
      if (webview) {
        // YouTube will handle its own theme based on URL parameters
        // We can inject CSS or send messages if needed
        this.log(`Webview theme updated to: ${effectiveTheme}`);
      }
    } catch (error) {
      console.error('Failed to update webview theme:', error);
    }
  }

  /**
   * Update settings modal theme selector
   */
  updateSettingsThemeSelector(theme) {
    try {
      const themeSelect = document.getElementById('theme');
      if (themeSelect && themeSelect.value !== theme) {
        themeSelect.value = theme;
        
        // Update description
        const description = document.getElementById('themeDescription');
        if (description) {
          const descriptions = {
            'light': 'Light theme with bright colors',
            'dark': 'Dark theme with muted colors',
            'auto': 'Follows your system preference'
          };
          description.textContent = descriptions[theme] || descriptions.auto;
        }
      }
    } catch (error) {
      console.error('Failed to update settings theme selector:', error);
    }
  }

  /**
   * Notify external systems
   */
  notifyExternalSystems(theme, effectiveTheme) {
    try {
      // Notify main process via IPC
      if (window.electronAPI && window.electronAPI.themeChanged) {
        window.electronAPI.themeChanged(theme, effectiveTheme);
      }

    } catch (error) {
      console.error('Failed to notify external systems:', error);
    }
  }

  /**
   * Start periodic sync (fallback mechanism)
   */
  startPeriodicSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(() => {
      try {
        this.checkForThemeChanges();
      } catch (error) {
        console.error('Periodic sync error:', error);
      }
    }, this.options.syncInterval);
  }

  /**
   * Check for theme changes (fallback)
   */
  checkForThemeChanges() {
    try {
      let currentTheme = null;

      if (this.appSettings?.getTheme) {
        currentTheme = this.appSettings.getTheme();
      } else {
        const stored = localStorage.getItem(this.options.storageKey);
        if (stored) {
          const data = JSON.parse(stored);
          currentTheme = data.theme;
        }
      }

      if (currentTheme && currentTheme !== this.lastKnownTheme) {
        this.log(`Detected theme change via periodic sync: ${currentTheme}`);
        this.syncFromAppSettings(currentTheme);
      }

    } catch (error) {
      // Silent fail for periodic checks
    }
  }

  /**
   * Get current integration status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasThemeManager: !!this.themeManager,
      hasAppSettings: !!this.appSettings,
      lastKnownTheme: this.lastKnownTheme,
      currentTheme: this.themeManager?.getTheme(),
      effectiveTheme: this.themeManager?.getEffectiveTheme(),
      syncHealth: this.checkSyncHealth()
    };
  }

  /**
   * Check synchronization health between systems
   */
  async checkSyncHealth() {
    try {
      const health = {
        themeManagerWorking: false,
        appSettingsWorking: false,
        storageWorking: false,
        syncWorking: false,
        issues: []
      };

      // Check theme manager
      if (this.themeManager) {
        try {
          const theme = this.themeManager.getTheme();
          if (['light', 'dark', 'auto'].includes(theme)) {
            health.themeManagerWorking = true;
          } else {
            health.issues.push('ThemeManager returning invalid theme');
          }
        } catch (error) {
          health.issues.push(`ThemeManager error: ${error.message}`);
        }
      } else {
        health.issues.push('ThemeManager not available');
      }

      // Check app settings
      if (this.appSettings) {
        try {
          const theme = await this.appSettings.getTheme();
          if (['light', 'dark', 'auto'].includes(theme)) {
            health.appSettingsWorking = true;
          } else {
            health.issues.push('App settings returning invalid theme');
          }
        } catch (error) {
          health.issues.push(`App settings error: ${error.message}`);
        }
      } else {
        health.issues.push('App settings not available');
      }

      // Check storage
      try {
        const testKey = 'theme-health-check';
        localStorage.setItem(testKey, 'test');
        const retrieved = localStorage.getItem(testKey);
        localStorage.removeItem(testKey);

        if (retrieved === 'test') {
          health.storageWorking = true;
        } else {
          health.issues.push('Storage retrieval failed');
        }
      } catch (error) {
        health.issues.push(`Storage error: ${error.message}`);
      }

      // Check sync
      if (health.themeManagerWorking && health.appSettingsWorking) {
        try {
          const managerTheme = this.themeManager.getTheme();
          const appTheme = await this.appSettings.getTheme();

          if (managerTheme === appTheme) {
            health.syncWorking = true;
          } else {
            health.issues.push(`Theme sync mismatch: Manager(${managerTheme}) vs App(${appTheme})`);
          }
        } catch (error) {
          health.issues.push(`Sync check error: ${error.message}`);
        }
      }

      return health;
    } catch (error) {
      return {
        themeManagerWorking: false,
        appSettingsWorking: false,
        storageWorking: false,
        syncWorking: false,
        issues: [`Health check failed: ${error.message}`]
      };
    }
  }

  /**
   * Repair synchronization issues
   */
  async repairSync() {
    try {
      const health = await this.checkSyncHealth();
      let repaired = false;

      this.log('Starting sync repair...');

      // Repair theme manager issues
      if (!health.themeManagerWorking && this.themeManager) {
        try {
          this.themeManager.fallbackToSystemTheme();
          repaired = true;
          this.log('Repaired theme manager');
        } catch (error) {
          this.log(`Failed to repair theme manager: ${error.message}`);
        }
      }

      // Repair sync mismatch
      if (!health.syncWorking && health.themeManagerWorking) {
        try {
          const managerTheme = this.themeManager.getTheme();
          if (this.appSettings?.setTheme) {
            await this.appSettings.setTheme(managerTheme);
            repaired = true;
            this.log(`Repaired sync mismatch by setting app theme to: ${managerTheme}`);
          }
        } catch (error) {
          this.log(`Failed to repair sync: ${error.message}`);
        }
      }

      // Repair storage issues
      if (!health.storageWorking) {
        try {
          this.enableMemoryStorage();
          repaired = true;
          this.log('Enabled memory storage fallback');
        } catch (error) {
          this.log(`Failed to enable memory storage: ${error.message}`);
        }
      }

      return repaired;
    } catch (error) {
      this.log(`Sync repair failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Enable memory storage fallback
   */
  enableMemoryStorage() {
    if (!this.memoryStorage) {
      this.memoryStorage = new Map();
      this.log('Memory storage enabled');
    }
  }

  /**
   * Debug logging
   */
  log(message) {
    if (this.options.enableDebugLogging) {
      console.log(`[ThemeIntegration] ${message}`);
    }
  }

  /**
   * Destroy the integration
   */
  destroy() {
    try {
      if (this.syncTimer) {
        clearInterval(this.syncTimer);
        this.syncTimer = null;
      }

      this.isInitialized = false;
      this.log('ThemeIntegration destroyed');

    } catch (error) {
      console.error('Failed to destroy ThemeIntegration:', error);
    }
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeIntegration;
} else if (typeof window !== 'undefined') {
  window.ThemeIntegration = ThemeIntegration;
}
