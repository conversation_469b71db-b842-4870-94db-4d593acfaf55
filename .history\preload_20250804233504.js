const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Settings management
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),

  // Theme management
  getTheme: () => ipcRenderer.invoke('get-theme'),
  setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
  saveTheme: (theme) => ipcRenderer.invoke('set-theme', theme),

  // Video quality management
  getVideoQuality: () => ipcRenderer.invoke('get-video-quality'),
  setVideoQuality: (quality) => ipcRenderer.invoke('set-video-quality', quality),

  // Event listeners
  onVideoQualityChanged: (callback) => {
    ipcRenderer.on('video-quality-changed', (event, quality) => callback(quality));
  },

  onThemeChange: (callback) => {
    ipcRenderer.on('theme-changed', (event, theme) => callback(theme));
  },

  onSystemThemeChange: (callback) => {
    ipcRenderer.on('system-theme-changed', (event, isDark) => callback(isDark));
  },

  onOpenSettings: (callback) => {
    ipcRenderer.on('open-settings', () => callback());
  },

  // Notify main process of theme changes
  themeChanged: (theme, effectiveTheme) => {
    ipcRenderer.send('renderer-theme-changed', { theme, effectiveTheme });
  },

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});