// Arabic language resources for YouTube Player App
// Following YouTube's i18n resource loading pattern

export const ar = {
    // Application metadata
    meta: {
        language: 'ar',
        direction: 'rtl',
        locale: 'ar-EG',
        name: 'العربية'
    },

    // Header and navigation
    header: {
        goBack: 'العودة',
        goForward: 'التقدم',
        reload: 'إعادة التحميل',
        toggleTheme: 'تبديل المظهر',
        settings: 'الإعدادات',
        toggleFullscreen: 'تبديل الشاشة الكاملة',
        urlPlaceholder: 'أدخل رابط يوتيوب أو ابحث...',
        goButton: 'تحميل فيديو يوتيوب'
    },

    // Settings modal
    settings: {
        title: 'الإعدادات',
        close: 'إغلاق الإعدادات',
        save: 'حفظ الإعدادات',
        cancel: 'إلغاء',
        
        // Sections
        videoQuality: 'جودة الفيديو',
        playerSettings: 'إعدادات المشغل',
        appearance: 'المظهر',
        language: 'اللغة والمنطقة',
        
        // Video quality options
        qualityAuto: 'تلقائي',
        quality144p: '144p',
        quality240p: '240p',
        quality360p: '360p',
        quality480p: '480p',
        quality720p: '720p',
        quality1080p: '1080p',
        quality4k: '4K',
        
        // Player settings
        autoplay: 'التشغيل التلقائي',
        defaultVolume: 'مستوى الصوت الافتراضي',
        
        // Appearance settings
        theme: 'المظهر',
        themeLight: 'فاتح',
        themeDark: 'داكن',
        themeAuto: 'تلقائي (النظام)',
        
        // Language settings
        languageLabel: 'لغة الواجهة',
        languageEnglish: 'الإنجليزية (English)',
        languageArabic: 'العربية',
        
        // Theme descriptions
        themeLightDesc: 'مظهر فاتح بألوان مشرقة',
        themeDarkDesc: 'مظهر داكن بألوان هادئة',
        themeAutoDesc: 'يتبع إعدادات النظام'
    },

    // Quality overlay
    quality: {
        title: 'جودة الفيديو',
        close: 'إغلاق محدد الجودة',
        applying: 'تطبيق إعدادات الجودة...',
        applied: 'تم تعيين الجودة إلى {quality}',
        failed: 'فشل في تطبيق إعدادات الجودة'
    },

    // Notifications and messages
    notifications: {
        settingsSaved: 'تم حفظ الإعدادات بنجاح!',
        settingsFailed: 'فشل في حفظ الإعدادات',
        themeChanged: 'تم تغيير المظهر إلى {theme}',
        languageChanged: 'تم تغيير اللغة إلى العربية',
        languageApplying: 'تطبيق اللغة العربية على يوتيوب...',
        qualityChanged: 'تم تعيين جودة الفيديو إلى {quality}',
        loading: 'جاري التحميل...',
        error: 'حدث خطأ',
        success: 'تمت العملية بنجاح',
        webviewCrashed: 'تعطل مشغل الفيديو. يرجى إعادة التحميل.',
        loadFailed: 'فشل في تحميل يوتيوب',
        connectionError: 'خطأ في الاتصال. يرجى التحقق من الإنترنت.',
        
        // Theme-specific messages
        themeApplying: 'تطبيق مظهر {theme}...',
        themeApplied: 'تم تطبيق مظهر {theme} بنجاح',
        deviceThemeDetected: 'تم اكتشاف مظهر الجهاز: {theme}',
        
        // Language-specific messages
        languageDetecting: 'اكتشاف لغة النظام...',
        languageDetected: 'تم اكتشاف لغة النظام: {language}',
        rtlEnabled: 'تم تفعيل التخطيط من اليمين إلى اليسار',
        ltrEnabled: 'تم تفعيل التخطيط من اليسار إلى اليمين'
    },

    // Loading states
    loading: {
        default: 'جاري التحميل...',
        settings: 'تحميل الإعدادات...',
        theme: 'تطبيق المظهر...',
        language: 'تغيير اللغة...',
        quality: 'ضبط جودة الفيديو...',
        youtube: 'تحميل يوتيوب...',
        webview: 'تهيئة المشغل...'
    },

    // Error messages
    errors: {
        settingsLoad: 'فشل في تحميل الإعدادات',
        settingsSave: 'فشل في حفظ الإعدادات',
        themeApply: 'فشل في تطبيق المظهر',
        languageApply: 'فشل في تغيير اللغة',
        qualityApply: 'فشل في تعيين جودة الفيديو',
        webviewError: 'حدث خطأ في المشغل',
        networkError: 'خطأ في اتصال الشبكة',
        unknownError: 'حدث خطأ غير معروف'
    },

    // Accessibility labels
    accessibility: {
        mainContent: 'محتوى الفيديو الرئيسي',
        settingsModal: 'مربع حوار الإعدادات',
        qualityOverlay: 'محدد جودة الفيديو',
        themeToggle: 'زر تبديل المظهر',
        languageSelector: 'محدد اللغة',
        qualitySelector: 'محدد الجودة',
        volumeSlider: 'شريط تمرير الصوت',
        closeButton: 'زر الإغلاق',
        saveButton: 'زر الحفظ',
        cancelButton: 'زر الإلغاء'
    },

    // Keyboard shortcuts
    shortcuts: {
        openSettings: 'فتح الإعدادات (Ctrl+,)',
        toggleTheme: 'تبديل المظهر (Ctrl+T)',
        toggleFullscreen: 'تبديل الشاشة الكاملة (F11)',
        reload: 'إعادة التحميل (Ctrl+R)',
        goBack: 'العودة (Alt+Left)',
        goForward: 'التقدم (Alt+Right)'
    },

    // Time and date formatting
    time: {
        now: 'الآن',
        secondsAgo: 'منذ {count} ثانية',
        minutesAgo: 'منذ {count} دقيقة',
        hoursAgo: 'منذ {count} ساعة',
        daysAgo: 'منذ {count} يوم'
    }
};

// Export default for easier importing
export default ar;
