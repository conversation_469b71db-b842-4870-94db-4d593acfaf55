// English language resources for YouTube Player App
// Following YouTube's i18n resource loading pattern

export const en = {
    // Application metadata
    meta: {
        language: 'en',
        direction: 'ltr',
        locale: 'en-US',
        name: 'English'
    },

    // Header and navigation
    header: {
        goBack: 'Go Back',
        goForward: 'Go Forward',
        reload: 'Reload',
        toggleTheme: 'Toggle Theme',
        settings: 'Settings',
        toggleFullscreen: 'Toggle Fullscreen',
        urlPlaceholder: 'Enter YouTube URL or search...',
        goButton: 'Load YouTube video'
    },

    // Settings modal
    settings: {
        title: 'Settings',
        close: 'Close Settings',
        save: 'Save Settings',
        cancel: 'Cancel',
        
        // Sections
        videoQuality: 'Video Quality',
        playerSettings: 'Player Settings',
        appearance: 'Appearance',
        language: 'Language & Region',
        
        // Video quality options
        qualityAuto: 'Auto',
        quality144p: '144p',
        quality240p: '240p',
        quality360p: '360p',
        quality480p: '480p',
        quality720p: '720p',
        quality1080p: '1080p',
        quality4k: '4K',
        
        // Player settings
        autoplay: 'Autoplay',
        defaultVolume: 'Default Volume',
        
        // Appearance settings
        theme: 'Theme',
        themeLight: 'Light',
        themeDark: 'Dark',
        themeAuto: 'Auto (System)',
        
        // Language settings
        languageLabel: 'Interface Language',
        languageEnglish: 'English',
        languageArabic: 'العربية (Arabic)',
        
        // Theme descriptions
        themeLightDesc: 'Light theme with bright colors',
        themeDarkDesc: 'Dark theme with muted colors',
        themeAutoDesc: 'Follows your system preference'
    },

    // Quality overlay
    quality: {
        title: 'Video Quality',
        close: 'Close quality selector',
        applying: 'Applying quality settings...',
        applied: 'Quality set to {quality}',
        failed: 'Failed to apply quality settings'
    },

    // Notifications and messages
    notifications: {
        settingsSaved: 'Settings saved successfully!',
        settingsFailed: 'Failed to save settings',
        themeChanged: 'Theme changed to {theme}',
        languageChanged: 'Language changed to English',
        languageApplying: 'Applying English language to YouTube...',
        qualityChanged: 'Video quality set to {quality}',
        loading: 'Loading...',
        error: 'An error occurred',
        success: 'Operation completed successfully',
        webviewCrashed: 'Video player crashed. Please reload.',
        loadFailed: 'Failed to load YouTube',
        connectionError: 'Connection error. Please check your internet.',
        
        // Theme-specific messages
        themeApplying: 'Applying {theme} theme...',
        themeApplied: '{theme} theme applied successfully',
        deviceThemeDetected: 'Device theme detected: {theme}',
        
        // Language-specific messages
        languageDetecting: 'Detecting system language...',
        languageDetected: 'System language detected: {language}',
        rtlEnabled: 'Right-to-left layout enabled',
        ltrEnabled: 'Left-to-right layout enabled'
    },

    // Loading states
    loading: {
        default: 'Loading...',
        settings: 'Loading settings...',
        theme: 'Applying theme...',
        language: 'Changing language...',
        quality: 'Adjusting video quality...',
        youtube: 'Loading YouTube...',
        webview: 'Initializing player...'
    },

    // Error messages
    errors: {
        settingsLoad: 'Failed to load settings',
        settingsSave: 'Failed to save settings',
        themeApply: 'Failed to apply theme',
        languageApply: 'Failed to change language',
        qualityApply: 'Failed to set video quality',
        webviewError: 'Player error occurred',
        networkError: 'Network connection error',
        unknownError: 'An unknown error occurred'
    },

    // Accessibility labels
    accessibility: {
        mainContent: 'Main video content',
        settingsModal: 'Settings dialog',
        qualityOverlay: 'Video quality selector',
        themeToggle: 'Theme toggle button',
        languageSelector: 'Language selector',
        qualitySelector: 'Quality selector',
        volumeSlider: 'Volume slider',
        closeButton: 'Close button',
        saveButton: 'Save button',
        cancelButton: 'Cancel button'
    },

    // Keyboard shortcuts
    shortcuts: {
        openSettings: 'Open Settings (Ctrl+,)',
        toggleTheme: 'Toggle Theme (Ctrl+T)',
        toggleFullscreen: 'Toggle Fullscreen (F11)',
        reload: 'Reload (Ctrl+R)',
        goBack: 'Go Back (Alt+Left)',
        goForward: 'Go Forward (Alt+Right)'
    },

    // Time and date formatting
    time: {
        now: 'now',
        secondsAgo: '{count} seconds ago',
        minutesAgo: '{count} minutes ago',
        hoursAgo: '{count} hours ago',
        daysAgo: '{count} days ago'
    }
};

// Export default for easier importing
export default en;
