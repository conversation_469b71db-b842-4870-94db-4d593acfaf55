const { app, BrowserWindow, ipcMain, Menu, shell, nativeTheme } = require('electron');
const path = require('path');
const Store = require('electron-store');

// Initialize electron-store for settings persistence
const store = new Store({
  defaults: {
    theme: 'auto',
    windowBounds: { width: 1200, height: 800 },
    quality: 'auto',
    volume: 100,
    autoplay: true,
    language: 'ar'
  }
});

let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webviewTag: true, // Enable webview tag
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    titleBarStyle: 'default',
    show: false
  });

  // Load the index.html file
  mainWindow.loadFile('renderer/index.html');

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Open external links in default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
  
  // Enable web security for YouTube
  mainWindow.webContents.session.webRequest.onBeforeSendHeaders((details, callback) => {
    // Allow YouTube to load properly
    if (details.url.includes('youtube.com') || details.url.includes('ytimg.com') || details.url.includes('googlevideo.com')) {
      callback({ requestHeaders: details.requestHeaders });
    } else {
      callback({ requestHeaders: details.requestHeaders });
    }
  });

  // Create application menu
  createMenu();
}

function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Settings',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.send('open-settings');
          }
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC handlers for settings and video quality
ipcMain.handle('get-settings', () => {
  return {
    videoQuality: store.get('videoQuality', 'auto'),
    autoplay: store.get('autoplay', true),
    volume: store.get('volume', 100),
    theme: store.get('theme', 'auto'),
    language: store.get('language', 'ar')
  };
});

// Theme-specific IPC handlers
ipcMain.handle('get-theme', () => {
  return store.get('theme', 'auto');
});

ipcMain.handle('set-theme', (event, theme) => {
  try {
    // Validate theme
    const validThemes = ['light', 'dark', 'auto'];
    if (!validThemes.includes(theme)) {
      throw new Error(`Invalid theme: ${theme}`);
    }

    // Save theme
    store.set('theme', theme);

    // Update native theme if needed
    if (theme === 'dark') {
      nativeTheme.themeSource = 'dark';
    } else if (theme === 'light') {
      nativeTheme.themeSource = 'light';
    } else {
      nativeTheme.themeSource = 'system';
    }

    // Notify all windows
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('theme-changed', theme);
    });

    return { success: true, theme };
  } catch (error) {
    console.error('Failed to set theme:', error);
    return { success: false, error: error.message };
  }
});

// Listen for system theme changes
nativeTheme.on('updated', () => {
  const currentTheme = store.get('theme', 'auto');
  if (currentTheme === 'auto') {
    // Notify all windows of system theme change
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('system-theme-changed', nativeTheme.shouldUseDarkColors);
    });
  }
});

ipcMain.handle('save-settings', async (event, settings) => {
  try {
    // Validate settings
    const validatedSettings = {
      quality: settings.quality || 'auto',
      volume: Math.max(0, Math.min(100, settings.volume || 100)),
      autoplay: Boolean(settings.autoplay),
      theme: ['light', 'dark', 'auto'].includes(settings.theme) ? settings.theme : 'auto',
      language: settings.language || 'ar'
    };

    const oldTheme = store.get('theme', 'auto');

    // Save each setting
    Object.entries(validatedSettings).forEach(([key, value]) => {
      store.set(key, value);
    });

    // If theme changed, update native theme and notify all windows
    if (validatedSettings.theme !== oldTheme) {
      if (validatedSettings.theme === 'dark') {
        nativeTheme.themeSource = 'dark';
      } else if (validatedSettings.theme === 'light') {
        nativeTheme.themeSource = 'light';
      } else {
        nativeTheme.themeSource = 'system';
      }

      // Notify all windows of theme change
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send('theme-changed', validatedSettings.theme);
      });
    }

    return { success: true, settings: validatedSettings };
  } catch (error) {
    console.error('Failed to save settings:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('set-video-quality', (event, quality) => {
  try {
    store.set('videoQuality', quality);
    // Send quality change to renderer
    mainWindow.webContents.send('video-quality-changed', quality);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-video-quality', () => {
  return store.get('videoQuality', 'auto');
});

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
}); 