/**
 * Theme System Validation Script
 * Comprehensive validation for the YouTube-style theme system
 * Run this script to verify the entire system is working correctly
 */

class ThemeSystemValidator {
  constructor() {
    this.results = [];
    this.errors = [];
    this.warnings = [];
  }

  /**
   * Run complete validation
   */
  async validate() {
    console.log('🔍 Starting comprehensive theme system validation...');
    
    try {
      // File existence checks
      await this.validateFileStructure();
      
      // JavaScript object availability
      this.validateJavaScriptObjects();
      
      // CSS system validation
      this.validateCSSSystem();
      
      // Theme manager validation
      await this.validateThemeManager();
      
      // Integration validation
      await this.validateIntegration();
      
      // UI validation
      this.validateUI();
      
      // Performance validation
      await this.validatePerformance();
      
      // Generate report
      this.generateValidationReport();
      
    } catch (error) {
      this.addError('Validation failed', error.message);
      console.error('Validation failed:', error);
    }
  }

  /**
   * Validate file structure
   */
  async validateFileStructure() {
    const requiredFiles = [
      'renderer/theme/theme-system.css',
      'renderer/theme/theme-manager.js',
      'renderer/theme/theme-integration.js',
      'renderer/theme/theme-testing.js'
    ];

    for (const file of requiredFiles) {
      try {
        // Check if file is loaded by looking for expected content
        if (file.endsWith('.css')) {
          const styleSheets = Array.from(document.styleSheets);
          const found = styleSheets.some(sheet => 
            sheet.href && sheet.href.includes(file.split('/').pop())
          );
          
          if (found) {
            this.addResult(`✅ ${file} loaded`);
          } else {
            this.addError(`❌ ${file} not loaded`);
          }
        } else if (file.endsWith('.js')) {
          // Check for expected global objects
          const fileName = file.split('/').pop().replace('.js', '');
          const expectedObjects = {
            'theme-manager': 'ThemeManager',
            'theme-integration': 'ThemeIntegration',
            'theme-testing': 'ThemeSystemTester'
          };
          
          const objectName = expectedObjects[fileName];
          if (objectName && typeof window[objectName] !== 'undefined') {
            this.addResult(`✅ ${file} loaded (${objectName} available)`);
          } else {
            this.addError(`❌ ${file} not loaded or ${objectName} not available`);
          }
        }
      } catch (error) {
        this.addError(`Error checking ${file}`, error.message);
      }
    }
  }

  /**
   * Validate JavaScript objects
   */
  validateJavaScriptObjects() {
    const requiredObjects = [
      'ThemeManager',
      'ThemeIntegration',
      'ThemeSystemTester'
    ];

    requiredObjects.forEach(obj => {
      if (typeof window[obj] !== 'undefined') {
        this.addResult(`✅ ${obj} class available`);
      } else {
        this.addError(`❌ ${obj} class not available`);
      }
    });

    // Check for global testing functions
    const testingFunctions = [
      'runThemeTests',
      'checkThemeHealth',
      'repairThemeSystem',
      'getThemeDebugInfo'
    ];

    testingFunctions.forEach(func => {
      if (typeof window[func] === 'function') {
        this.addResult(`✅ ${func}() function available`);
      } else {
        this.addWarning(`⚠️ ${func}() function not available`);
      }
    });
  }

  /**
   * Validate CSS system
   */
  validateCSSSystem() {
    // Check CSS variables
    const requiredVariables = [
      '--theme-bg-primary',
      '--theme-bg-secondary',
      '--theme-text-primary',
      '--theme-text-secondary',
      '--theme-border-primary',
      '--theme-accent-primary'
    ];

    const computedStyle = getComputedStyle(document.documentElement);
    
    requiredVariables.forEach(variable => {
      const value = computedStyle.getPropertyValue(variable).trim();
      if (value) {
        this.addResult(`✅ CSS variable ${variable}: ${value}`);
      } else {
        this.addError(`❌ CSS variable ${variable} not defined`);
      }
    });

    // Check theme classes
    const themeClasses = [
      'theme-card',
      'theme-button',
      'theme-input',
      'theme-bg-primary',
      'theme-text-primary'
    ];

    // Create test element to check if classes exist
    const testEl = document.createElement('div');
    document.body.appendChild(testEl);

    themeClasses.forEach(className => {
      testEl.className = className;
      const styles = getComputedStyle(testEl);
      
      // Check if class has any meaningful styles
      if (styles.backgroundColor !== 'rgba(0, 0, 0, 0)' || 
          styles.color !== 'rgba(0, 0, 0, 0)' ||
          styles.borderColor !== 'rgba(0, 0, 0, 0)') {
        this.addResult(`✅ Theme class .${className} has styles`);
      } else {
        this.addWarning(`⚠️ Theme class .${className} may not have styles`);
      }
    });

    document.body.removeChild(testEl);
  }

  /**
   * Validate theme manager
   */
  async validateThemeManager() {
    if (typeof window.ThemeManager === 'undefined') {
      this.addError('ThemeManager not available for validation');
      return;
    }

    try {
      // Create test instance
      const testManager = new window.ThemeManager({
        storageKey: 'validation-test-theme',
        defaultTheme: 'auto'
      });

      // Test basic functionality
      const themes = ['light', 'dark', 'auto'];
      for (const theme of themes) {
        const success = testManager.setTheme(theme);
        if (success) {
          this.addResult(`✅ ThemeManager can set ${theme} theme`);
        } else {
          this.addError(`❌ ThemeManager failed to set ${theme} theme`);
        }
      }

      // Test getters
      const currentTheme = testManager.getTheme();
      const effectiveTheme = testManager.getEffectiveTheme();
      const systemTheme = testManager.getSystemTheme();

      if (['light', 'dark', 'auto'].includes(currentTheme)) {
        this.addResult(`✅ ThemeManager.getTheme() returns valid theme: ${currentTheme}`);
      } else {
        this.addError(`❌ ThemeManager.getTheme() returns invalid theme: ${currentTheme}`);
      }

      if (['light', 'dark'].includes(effectiveTheme)) {
        this.addResult(`✅ ThemeManager.getEffectiveTheme() returns valid theme: ${effectiveTheme}`);
      } else {
        this.addError(`❌ ThemeManager.getEffectiveTheme() returns invalid theme: ${effectiveTheme}`);
      }

      if (['light', 'dark'].includes(systemTheme)) {
        this.addResult(`✅ ThemeManager.getSystemTheme() returns valid theme: ${systemTheme}`);
      } else {
        this.addError(`❌ ThemeManager.getSystemTheme() returns invalid theme: ${systemTheme}`);
      }

      // Test validation methods
      if (typeof testManager.validateThemeSystem === 'function') {
        const validation = testManager.validateThemeSystem();
        if (validation.isValid) {
          this.addResult('✅ ThemeManager validation passed');
        } else {
          this.addWarning(`⚠️ ThemeManager validation issues: ${validation.issues.join(', ')}`);
        }
      }

      // Clean up
      testManager.destroy();

    } catch (error) {
      this.addError('ThemeManager validation failed', error.message);
    }
  }

  /**
   * Validate integration
   */
  async validateIntegration() {
    if (typeof window.ThemeIntegration === 'undefined') {
      this.addError('ThemeIntegration not available for validation');
      return;
    }

    try {
      // Create test instances
      const testManager = new window.ThemeManager({
        storageKey: 'validation-test-integration',
        defaultTheme: 'light'
      });

      const testIntegration = new window.ThemeIntegration({
        themeManager: testManager,
        enableDebugLogging: false
      });

      // Test status
      const status = testIntegration.getStatus();
      if (status.isInitialized) {
        this.addResult('✅ ThemeIntegration initialized successfully');
      } else {
        this.addError('❌ ThemeIntegration failed to initialize');
      }

      if (status.hasThemeManager) {
        this.addResult('✅ ThemeIntegration has ThemeManager reference');
      } else {
        this.addError('❌ ThemeIntegration missing ThemeManager reference');
      }

      // Test health check
      if (typeof testIntegration.checkSyncHealth === 'function') {
        const health = await testIntegration.checkSyncHealth();
        if (health.issues.length === 0) {
          this.addResult('✅ ThemeIntegration health check passed');
        } else {
          this.addWarning(`⚠️ ThemeIntegration health issues: ${health.issues.join(', ')}`);
        }
      }

      // Clean up
      testIntegration.destroy();
      testManager.destroy();

    } catch (error) {
      this.addError('ThemeIntegration validation failed', error.message);
    }
  }

  /**
   * Validate UI elements
   */
  validateUI() {
    // Check for theme toggle button
    const themeToggleBtn = document.getElementById('themeToggleBtn');
    if (themeToggleBtn) {
      this.addResult('✅ Theme toggle button found');
      
      const icon = themeToggleBtn.querySelector('i');
      if (icon) {
        this.addResult('✅ Theme toggle button has icon');
      } else {
        this.addWarning('⚠️ Theme toggle button missing icon');
      }
    } else {
      this.addWarning('⚠️ Theme toggle button not found');
    }

    // Check for settings modal
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) {
      this.addResult('✅ Settings modal found');
      
      const themeSelect = document.getElementById('theme');
      if (themeSelect) {
        this.addResult('✅ Theme selector found in settings');
      } else {
        this.addWarning('⚠️ Theme selector not found in settings');
      }
    } else {
      this.addWarning('⚠️ Settings modal not found');
    }

    // Check meta theme-color
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      this.addResult(`✅ Meta theme-color found: ${metaThemeColor.content}`);
    } else {
      this.addWarning('⚠️ Meta theme-color not found');
    }
  }

  /**
   * Validate performance
   */
  async validatePerformance() {
    if (typeof window.ThemeManager === 'undefined') {
      this.addWarning('Cannot validate performance - ThemeManager not available');
      return;
    }

    try {
      const testManager = new window.ThemeManager({
        storageKey: 'validation-perf-test',
        defaultTheme: 'light'
      });

      // Test theme switching performance
      const iterations = 5;
      const startTime = performance.now();

      for (let i = 0; i < iterations; i++) {
        testManager.setTheme(i % 2 === 0 ? 'light' : 'dark');
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      const endTime = performance.now();
      const avgTime = (endTime - startTime) / iterations;

      if (avgTime < 50) {
        this.addResult(`✅ Theme switching performance good: ${avgTime.toFixed(2)}ms average`);
      } else if (avgTime < 100) {
        this.addWarning(`⚠️ Theme switching performance acceptable: ${avgTime.toFixed(2)}ms average`);
      } else {
        this.addError(`❌ Theme switching performance poor: ${avgTime.toFixed(2)}ms average`);
      }

      testManager.destroy();

    } catch (error) {
      this.addError('Performance validation failed', error.message);
    }
  }

  /**
   * Add validation result
   */
  addResult(message) {
    this.results.push(message);
    console.log(message);
  }

  /**
   * Add error
   */
  addError(message, details = '') {
    const fullMessage = details ? `${message}: ${details}` : message;
    this.errors.push(fullMessage);
    console.error(fullMessage);
  }

  /**
   * Add warning
   */
  addWarning(message, details = '') {
    const fullMessage = details ? `${message}: ${details}` : message;
    this.warnings.push(fullMessage);
    console.warn(fullMessage);
  }

  /**
   * Generate validation report
   */
  generateValidationReport() {
    const totalChecks = this.results.length + this.errors.length + this.warnings.length;
    const successRate = ((this.results.length / totalChecks) * 100).toFixed(1);

    console.log('\n📊 Theme System Validation Report');
    console.log('=====================================');
    console.log(`Total Checks: ${totalChecks}`);
    console.log(`Passed: ${this.results.length}`);
    console.log(`Warnings: ${this.warnings.length}`);
    console.log(`Errors: ${this.errors.length}`);
    console.log(`Success Rate: ${successRate}%`);
    console.log('=====================================\n');

    if (this.errors.length > 0) {
      console.log('❌ Errors:');
      this.errors.forEach(error => console.log(`  - ${error}`));
      console.log('');
    }

    if (this.warnings.length > 0) {
      console.log('⚠️ Warnings:');
      this.warnings.forEach(warning => console.log(`  - ${warning}`));
      console.log('');
    }

    // Overall status
    if (this.errors.length === 0) {
      console.log('🎉 Theme system validation PASSED!');
    } else if (this.errors.length <= 2) {
      console.log('⚠️ Theme system validation passed with minor issues');
    } else {
      console.log('❌ Theme system validation FAILED - please address errors');
    }

    return {
      totalChecks,
      passed: this.results.length,
      warnings: this.warnings.length,
      errors: this.errors.length,
      successRate: parseFloat(successRate),
      status: this.errors.length === 0 ? 'PASSED' : this.errors.length <= 2 ? 'PASSED_WITH_WARNINGS' : 'FAILED'
    };
  }
}

// Auto-run validation when script is loaded
if (typeof window !== 'undefined') {
  window.ThemeSystemValidator = ThemeSystemValidator;
  
  // Add global validation function
  window.validateThemeSystem = async () => {
    const validator = new ThemeSystemValidator();
    return await validator.validate();
  };
  
  console.log('🔍 Theme System Validator loaded');
  console.log('Run validateThemeSystem() to validate the entire system');
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeSystemValidator;
}
