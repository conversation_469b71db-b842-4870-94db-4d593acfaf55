<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Theme Test - Realistic Simulation</title>
    <style>
        /* Realistic YouTube theme system simulation */
        :root {
            --yt-spec-base-background: #ffffff;
            --yt-spec-raised-background: #f9f9f9;
            --yt-spec-menu-background: #ffffff;
            --yt-spec-text-primary: #0f0f0f;
            --yt-spec-text-secondary: #606060;
            --yt-spec-general-background-a: #ffffff;
            --yt-spec-general-background-b: #f9f9f9;
            --yt-spec-wordmark-text: #000000;
            --yt-spec-call-to-action: #065fd4;
            --yt-spec-icon-active-other: #030303;
            --yt-spec-icon-inactive: #606060;
        }

        /* YouTube's actual dark theme CSS variables */
        html[dark] {
            --yt-spec-base-background: #0f0f0f;
            --yt-spec-raised-background: #212121;
            --yt-spec-menu-background: #282828;
            --yt-spec-text-primary: #ffffff;
            --yt-spec-text-secondary: #aaaaaa;
            --yt-spec-general-background-a: #0f0f0f;
            --yt-spec-general-background-b: #212121;
            --yt-spec-wordmark-text: #ffffff;
            --yt-spec-call-to-action: #3ea6ff;
            --yt-spec-icon-active-other: #ffffff;
            --yt-spec-icon-inactive: #aaaaaa;
        }

        /* Light theme explicit */
        html[light] {
            --yt-spec-base-background: #ffffff;
            --yt-spec-raised-background: #f9f9f9;
            --yt-spec-menu-background: #ffffff;
            --yt-spec-text-primary: #0f0f0f;
            --yt-spec-text-secondary: #606060;
            --yt-spec-general-background-a: #ffffff;
            --yt-spec-general-background-b: #f9f9f9;
            --yt-spec-wordmark-text: #000000;
            --yt-spec-call-to-action: #065fd4;
            --yt-spec-icon-active-other: #030303;
            --yt-spec-icon-inactive: #606060;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: var(--yt-spec-base-background);
            color: var(--yt-spec-text-primary);
            margin: 0;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: var(--yt-spec-raised-background);
            padding: 20px;
            border-radius: 8px;
        }

        .theme-info {
            background-color: var(--yt-spec-raised-background);
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid var(--yt-spec-text-primary);
        }

        button {
            background-color: var(--yt-spec-text-primary);
            color: var(--yt-spec-base-background);
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .status {
            font-weight: bold;
            margin: 10px 0;
        }

        .dark-indicator {
            display: none;
        }

        html[dark] .dark-indicator {
            display: block;
        }

        html[dark] .light-indicator {
            display: none;
        }

        /* Realistic YouTube structure simulation */
        ytd-app {
            background-color: var(--yt-spec-base-background);
            color: var(--yt-spec-text-primary);
            display: block;
            min-height: 100vh;
        }

        ytd-masthead {
            background-color: var(--yt-spec-raised-background);
            color: var(--yt-spec-text-primary);
            display: block;
            padding: 10px 20px;
            border-bottom: 1px solid var(--yt-spec-text-secondary);
        }

        ytd-guide-renderer {
            background-color: var(--yt-spec-menu-background);
            color: var(--yt-spec-text-primary);
            display: block;
            padding: 20px;
            width: 200px;
            float: left;
            min-height: 400px;
        }

        #content {
            background-color: var(--yt-spec-base-background);
            color: var(--yt-spec-text-primary);
            margin-left: 220px;
            padding: 20px;
            min-height: 400px;
        }

        .youtube-logo {
            color: var(--yt-spec-wordmark-text);
            font-weight: bold;
            font-size: 20px;
        }

        .video-item {
            background-color: var(--yt-spec-raised-background);
            color: var(--yt-spec-text-primary);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }

        .video-title {
            color: var(--yt-spec-text-primary);
            font-weight: bold;
            margin-bottom: 5px;
        }

        .video-meta {
            color: var(--yt-spec-text-secondary);
            font-size: 14px;
        }

        /* Simulate ytd-app element for backward compatibility */
        .ytd-app {
            background-color: var(--yt-spec-base-background);
            color: var(--yt-spec-text-primary);
            padding: 20px;
            margin: 20px 0;
            border: 2px solid var(--yt-spec-text-primary);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>YouTube Theme Test Page</h1>
        
        <div class="status">
            <div class="light-indicator">🌞 Light Theme Active</div>
            <div class="dark-indicator">🌙 Dark Theme Active</div>
        </div>

        <div class="theme-info">
            <h3>Current Theme Status:</h3>
            <p><strong>HTML dark attribute:</strong> <span id="dark-attr">-</span></p>
            <p><strong>Data-theme attribute:</strong> <span id="data-theme">-</span></p>
            <p><strong>Background color:</strong> <span id="bg-color">-</span></p>
            <p><strong>Text color:</strong> <span id="text-color">-</span></p>
        </div>

        <div class="ytd-app">
            <h3>Simulated ytd-app Element</h3>
            <p>This element simulates YouTube's main app container.</p>
            <p>It should change colors based on the theme.</p>
        </div>

        <div>
            <button onclick="applyDarkTheme()">Apply Dark Theme</button>
            <button onclick="applyLightTheme()">Apply Light Theme</button>
            <button onclick="toggleTheme()">Toggle Theme</button>
            <button onclick="updateStatus()">Update Status</button>
        </div>

        <div class="theme-info">
            <h3>Test Log:</h3>
            <div id="log"></div>
        </div>

        <div class="theme-info">
            <h3>Automatic Testing:</h3>
            <button onclick="runAutomaticTest()">Run Automatic Theme Test</button>
            <button onclick="clearLog()">Clear Log</button>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            console.log(message);
        }

        function updateStatus() {
            const html = document.documentElement;
            document.getElementById('dark-attr').textContent = html.hasAttribute('dark') ? 'Yes' : 'No';
            document.getElementById('data-theme').textContent = html.getAttribute('data-theme') || 'None';
            
            const styles = window.getComputedStyle(document.body);
            document.getElementById('bg-color').textContent = styles.backgroundColor;
            document.getElementById('text-color').textContent = styles.color;
            
            log(`Status updated - Dark: ${html.hasAttribute('dark')}, Data-theme: ${html.getAttribute('data-theme')}`);
        }

        function applyDarkTheme() {
            const html = document.documentElement;
            html.setAttribute('dark', '');
            html.setAttribute('data-theme', 'dark');
            html.style.colorScheme = 'dark';
            
            log('Dark theme applied');
            setTimeout(updateStatus, 100);
        }

        function applyLightTheme() {
            const html = document.documentElement;
            html.removeAttribute('dark');
            html.setAttribute('data-theme', 'light');
            html.style.colorScheme = 'light';
            
            log('Light theme applied');
            setTimeout(updateStatus, 100);
        }

        function toggleTheme() {
            const html = document.documentElement;
            if (html.hasAttribute('dark')) {
                applyLightTheme();
            } else {
                applyDarkTheme();
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('test-results').innerHTML = '';
            log('Log cleared');
        }

        async function runAutomaticTest() {
            log('🚀 Starting automatic theme test...');
            const results = [];

            const themes = [
                { name: 'dark', displayName: 'Dark Mode' },
                { name: 'light', displayName: 'Light Mode' }
            ];

            for (const theme of themes) {
                log(`🧪 Testing ${theme.displayName}...`);

                // Apply theme
                if (theme.name === 'dark') {
                    applyDarkTheme();
                } else {
                    applyLightTheme();
                }

                // Wait for application
                await new Promise(resolve => setTimeout(resolve, 500));

                // Verify theme
                const html = document.documentElement;
                const expectedDark = theme.name === 'dark';
                const actualDark = html.hasAttribute('dark');
                const dataTheme = html.getAttribute('data-theme');
                const styles = window.getComputedStyle(document.body);
                const bgColor = styles.backgroundColor;

                const success = expectedDark === actualDark && dataTheme === theme.name;

                const result = {
                    theme: theme.displayName,
                    success,
                    details: {
                        expectedDark,
                        actualDark,
                        dataTheme,
                        bgColor
                    }
                };

                results.push(result);

                if (success) {
                    log(`✅ ${theme.displayName} test PASSED`);
                } else {
                    log(`❌ ${theme.displayName} test FAILED - Expected dark: ${expectedDark}, Actual: ${actualDark}, Data-theme: ${dataTheme}`);
                }

                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Display final results
            const testResultsDiv = document.getElementById('test-results');
            const passedTests = results.filter(r => r.success).length;
            const totalTests = results.length;

            testResultsDiv.innerHTML = `
                <h4>Test Results: ${passedTests}/${totalTests} passed</h4>
                <ul>
                    ${results.map(r => `
                        <li style="color: ${r.success ? 'green' : 'red'}">
                            ${r.success ? '✅' : '❌'} ${r.theme}: ${r.success ? 'PASSED' : 'FAILED'}
                        </li>
                    `).join('')}
                </ul>
            `;

            log(`🎉 Automatic test completed: ${passedTests}/${totalTests} tests passed`);
        }

        // Initialize
        updateStatus();
        log('Theme test page loaded');

        // Auto-run test after a short delay
        setTimeout(() => {
            log('🔄 Auto-running theme test...');
            runAutomaticTest();
        }, 1000);
    </script>
</body>
</html>
