const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Settings management
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  
  // Video quality management
  getVideoQuality: () => ipcRenderer.invoke('get-video-quality'),
  setVideoQuality: (quality) => ipcRenderer.invoke('set-video-quality', quality),
  
  // Event listeners
  onVideoQualityChanged: (callback) => {
    ipcRenderer.on('video-quality-changed', (event, quality) => callback(quality));
  },
  
  onOpenSettings: (callback) => {
    ipcRenderer.on('open-settings', () => callback());
  },
  
  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
}); 