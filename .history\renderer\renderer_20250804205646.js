// YouTube Desktop Player Renderer Process
// Handles all UI interactions, settings modal, and video quality controls

class YouTubePlayer {
    constructor() {
        this.currentSettings = {};
        this.currentQuality = 'auto';
        this.isSettingsOpen = false;
        this.isQualityOverlayOpen = false;

        // Language application state management
        this.isApplyingLanguage = false;
        this.lastAppliedLanguage = null;

        // Translation system
        this.translations = {
            en: {
                // Header buttons
                'Go Back': 'Go Back',
                'Go Forward': 'Go Forward',
                'Reload': 'Reload',
                'Toggle Theme': 'Toggle Theme',
                'Settings': 'Settings',
                'Toggle Fullscreen': 'Toggle Fullscreen',
                'Enter YouTube URL or search...': 'Enter YouTube URL or search...',

                // Settings modal
                'Video Quality': 'Video Quality',
                'Player Settings': 'Player Settings',
                'Appearance': 'Appearance',
                'Auto': 'Auto',
                'Autoplay': 'Autoplay',
                'Default Volume': 'Default Volume',
                'Theme': 'Theme',
                'Language': 'Language',
                'Dark': 'Dark',
                'Light': 'Light',
                'Save Settings': 'Save Settings',
                'Cancel': 'Cancel',
                'Loading...': 'Loading...',
                'English': 'English',
                'Arabic': 'العربية'
            },
            ar: {
                // Header buttons
                'Go Back': 'العودة',
                'Go Forward': 'التقدم',
                'Reload': 'إعادة التحميل',
                'Toggle Theme': 'تبديل المظهر',
                'Settings': 'الإعدادات',
                'Toggle Fullscreen': 'تبديل الشاشة الكاملة',
                'Enter YouTube URL or search...': 'أدخل رابط يوتيوب أو ابحث...',

                // Settings modal
                'Video Quality': 'جودة الفيديو',
                'Player Settings': 'إعدادات المشغل',
                'Appearance': 'المظهر',
                'Auto': 'تلقائي',
                'Autoplay': 'التشغيل التلقائي',
                'Default Volume': 'مستوى الصوت الافتراضي',
                'Theme': 'المظهر',
                'Language': 'اللغة',
                'Dark': 'داكن',
                'Light': 'فاتح',
                'Save Settings': 'حفظ الإعدادات',
                'Cancel': 'إلغاء',
                'Loading...': 'جاري التحميل...',
                'English': 'الإنجليزية',
                'Arabic': 'العربية'
            }
        };

        this.initializeElements();
        this.bindEvents();
        this.loadSettings();
        this.setupElectronListeners();
        this.setupWebviewThemeListeners();
    }

    initializeElements() {
        // Main elements
        this.webview = document.getElementById('webview');
        this.urlInput = document.getElementById('urlInput');
        this.goBtn = document.getElementById('goBtn');
        
        // Navigation buttons
        this.backBtn = document.getElementById('backBtn');
        this.forwardBtn = document.getElementById('forwardBtn');
        this.reloadBtn = document.getElementById('reloadBtn');
        this.fullscreenBtn = document.getElementById('fullscreenBtn');
        
        // Settings elements
        this.settingsBtn = document.getElementById('settingsBtn');
        this.themeToggleBtn = document.getElementById('themeToggleBtn');
        this.settingsModal = document.getElementById('settingsModal');
        this.closeSettingsBtn = document.getElementById('closeSettingsBtn');
        this.saveSettingsBtn = document.getElementById('saveSettingsBtn');
        this.cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
        
        // Settings form elements
        this.qualityInputs = document.querySelectorAll('input[name="quality"]');
        this.autoplayInput = document.getElementById('autoplay');
        this.volumeInput = document.getElementById('volume');
        this.volumeValue = document.getElementById('volumeValue');
        this.themeSelect = document.getElementById('theme');
        this.languageSelect = document.getElementById('language');
        
        // Quality overlay elements
        this.qualityOverlay = document.getElementById('qualityOverlay');
        this.qualityButtons = document.querySelectorAll('.quality-btn');
        this.closeQualityBtn = document.getElementById('closeQualityBtn');
        
        // Loading indicator
        this.loadingIndicator = document.getElementById('loadingIndicator');
    }

    bindEvents() {
        // Navigation events
        this.backBtn.addEventListener('click', () => this.goBack());
        this.forwardBtn.addEventListener('click', () => this.goForward());
        this.reloadBtn.addEventListener('click', () => this.reload());
        this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        
        // URL bar events
        this.goBtn.addEventListener('click', () => this.navigate());
        this.urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.navigate();
        });
        
        // Settings modal events
        this.settingsBtn.addEventListener('click', () => this.openSettings());
        this.themeToggleBtn.addEventListener('click', () => this.toggleTheme());
        this.closeSettingsBtn.addEventListener('click', () => this.closeSettings());
        this.saveSettingsBtn.addEventListener('click', () => this.saveSettings());
        this.cancelSettingsBtn.addEventListener('click', () => this.closeSettings());
        
        // Settings form events
        this.qualityInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.currentQuality = e.target.value;
                this.updateQualityDisplay();
            });
        });
        
        this.volumeInput.addEventListener('input', (e) => {
            this.volumeValue.textContent = `${e.target.value}%`;
        });
        
        // Theme change event
        this.themeSelect.addEventListener('change', (e) => {
            const newTheme = e.target.value;
            this.applyTheme(newTheme);
        });

        // Language change event
        this.languageSelect.addEventListener('change', (e) => {
            const newLanguage = e.target.value;
            this.applyLanguage(newLanguage);
        });

        // Quality overlay events
        this.qualityButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const quality = e.target.dataset.quality;
                this.setVideoQuality(quality);
                this.closeQualityOverlay();
            });
        });
        
        this.closeQualityBtn.addEventListener('click', () => this.closeQualityOverlay());
        
        // Webview events
        this.webview.addEventListener('did-start-loading', () => {
            this.showLoading();
        });
        this.webview.addEventListener('did-stop-loading', () => {
            this.hideLoading();
        });
        this.webview.addEventListener('did-navigate', (e) => {
            this.updateUrlBar(e.url);

            // Apply quality settings to new videos
            if (e.url.includes('/watch')) {
                setTimeout(() => {
                    this.applyQualityToYouTube();
                }, 2000);
            }
        });
        this.webview.addEventListener('did-navigate-in-page', (e) => {
            this.updateUrlBar(e.url);

            // Apply quality settings to new videos (SPA navigation)
            if (e.url.includes('/watch')) {
                setTimeout(() => {
                    this.applyQualityToYouTube();
                }, 2000);
            }
        });
        this.webview.addEventListener('dom-ready', () => {
            // Apply quality settings when DOM is ready
            this.applyQualityToYouTube();
        });
        this.webview.addEventListener('did-fail-load', (e) => {
            console.error('Webview failed to load:', e);
            this.showNotification('Failed to load YouTube', 'error');
        });
        this.webview.addEventListener('crashed', () => {
            console.error('Webview crashed');
            this.showNotification('Webview crashed', 'error');
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Click outside modal to close
        this.settingsModal.addEventListener('click', (e) => {
            if (e.target === this.settingsModal) this.closeSettings();
        });
        
        this.qualityOverlay.addEventListener('click', (e) => {
            if (e.target === this.qualityOverlay) this.closeQualityOverlay();
        });
    }

    setupElectronListeners() {
        // Listen for settings open from main process
        window.electronAPI.onOpenSettings(() => {
            console.log('Received open-settings event from main process');
            this.openSettings();
        });
        
        // Listen for video quality changes from main process
        window.electronAPI.onVideoQualityChanged((quality) => {
            this.currentQuality = quality;
            this.updateQualityDisplay();
            this.applyQualityToYouTube();
        });
        
        // Set up webview event listeners for theme injection
        this.setupWebviewThemeListeners();
        
        // Test webview functionality after a short delay
        setTimeout(() => {
            this.testWebviewFunctionality();
        }, 2000);
        
        // Set up video quality monitoring
        this.setupQualityMonitoring();
    }
    
    setupWebviewThemeListeners() {
        if (!this.webview) {
            console.error('Webview not available for theme listeners');
            return;
        }

        // Listen for webview load events to apply theme and language
        this.webview.addEventListener('did-finish-load', () => {
            const currentTheme = this.currentSettings.theme || 'dark';
            const currentLanguage = this.currentSettings.language || 'ar';
            this.applyThemeToWebview(currentTheme);

            // Only ensure language on major page loads, with delay to avoid conflicts
            setTimeout(() => {
                this.ensureYouTubeLanguage(currentLanguage);
            }, 2000);
        });

        // Listen for navigation events - only apply theme, language will be handled by did-finish-load
        this.webview.addEventListener('did-navigate', () => {
            const currentTheme = this.currentSettings.theme || 'dark';
            this.applyThemeToWebview(currentTheme);
            // Don't apply language here to avoid conflicts
        });

        // Listen for navigation in page (SPA navigation) - minimal intervention
        this.webview.addEventListener('did-navigate-in-page', () => {
            const currentTheme = this.currentSettings.theme || 'dark';
            this.applyThemeToWebview(currentTheme);
            // Don't apply language for SPA navigation to avoid excessive reloads
        });

        // DOM ready - only apply theme
        this.webview.addEventListener('dom-ready', () => {
            const currentTheme = this.currentSettings.theme || 'dark';
            this.applyThemeToWebview(currentTheme);
            // Language will be handled by did-finish-load
        });

        // Stop loading - only apply theme
        this.webview.addEventListener('did-stop-loading', () => {
            const currentTheme = this.currentSettings.theme || 'dark';
            this.applyThemeToWebview(currentTheme);
            // Language will be handled by did-finish-load
        });
    }

    async loadSettings() {
        try {
            this.currentSettings = await window.electronAPI.getSettings();
            this.currentQuality = this.currentSettings.videoQuality || 'auto';
            
            // Update UI with loaded settings
            this.updateSettingsUI();
            this.updateQualityDisplay();
            
            // Apply saved theme settings
            const savedTheme = this.currentSettings.theme || 'dark';
            this.applyTheme(savedTheme);

            // Apply saved language settings
            const savedLanguage = this.currentSettings.language || 'ar';
            this.applyLanguage(savedLanguage);

            // Update initial webview URL with correct language if needed
            this.updateInitialWebviewLanguage(savedLanguage);

            // Apply saved quality settings to current video
            if (this.currentQuality !== 'auto') {
                setTimeout(() => {
                    this.applyQualityToYouTube();
                }, 3000);
            }
            
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    updateInitialWebviewLanguage(language) {
        if (!this.webview) return;

        try {
            const currentUrl = this.webview.getURL();

            // Only update if we're still on the initial YouTube page and language doesn't match
            if (currentUrl && currentUrl.includes('youtube.com')) {
                const expectedUrl = this.addLanguageParameterToUrl(currentUrl, language);

                // If the URL needs to be updated for the correct language
                if (expectedUrl !== currentUrl) {
                    this.webview.loadURL(expectedUrl);
                }
            }
        } catch (error) {
            // Silent fail - not critical
        }
    }

    updateSettingsUI() {
        // Set quality radio button
        const qualityInput = document.getElementById(`quality-${this.currentQuality}`);
        if (qualityInput) qualityInput.checked = true;
        
        // Set other settings
        this.autoplayInput.checked = this.currentSettings.autoplay || false;
        this.volumeInput.value = this.currentSettings.volume || 100;
        this.volumeValue.textContent = `${this.currentSettings.volume || 100}%`;
        this.themeSelect.value = this.currentSettings.theme || 'dark';
        this.languageSelect.value = this.currentSettings.language || 'ar';

        // Apply theme
        this.applyTheme(this.currentSettings.theme || 'dark');
    }

    updateQualityDisplay() {
        // Update quality buttons in overlay
        this.qualityButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.quality === this.currentQuality) {
                btn.classList.add('active');
            }
        });
    }
    
    applyTheme(theme) {
        // Remove existing theme classes
        document.body.removeAttribute('data-theme');

        // Apply new theme
        if (theme === 'auto') {
            // Auto theme follows system preference
            document.body.setAttribute('data-theme', 'auto');
        } else if (theme === 'light') {
            document.body.setAttribute('data-theme', 'light');
        } else {
            // Default to dark theme
            document.body.setAttribute('data-theme', 'dark');
        }

        // Store theme preference
        this.currentSettings.theme = theme;

        // Update theme toggle button icon
        this.updateThemeToggleIcon();

        // Apply theme to webview content
        this.applyThemeToWebview(theme);

        // Show notification
        this.showNotification(`Theme changed to ${theme}`, 'info');
    }

    applyLanguage(language) {
        // Set document direction for RTL support
        if (language === 'ar') {
            document.documentElement.setAttribute('dir', 'rtl');
            document.documentElement.setAttribute('lang', 'ar');
            document.body.setAttribute('dir', 'rtl');
        } else {
            document.documentElement.setAttribute('dir', 'ltr');
            document.documentElement.setAttribute('lang', 'en');
            document.body.setAttribute('dir', 'ltr');
        }

        // Apply translations to all UI elements
        this.applyTranslations(language);

        // Apply language to YouTube webview content
        this.applyLanguageToYouTube(language);

        // Show notification
        const message = language === 'ar' ? 'تم تغيير اللغة إلى العربية' : 'Language changed to English';
        this.showNotification(message, 'info');
    }

    applyTranslations(language) {
        const t = this.translations[language] || this.translations.en;

        // Update button titles
        document.getElementById('backBtn').setAttribute('title', t['Go Back']);
        document.getElementById('forwardBtn').setAttribute('title', t['Go Forward']);
        document.getElementById('reloadBtn').setAttribute('title', t['Reload']);
        document.getElementById('themeToggleBtn').setAttribute('title', t['Toggle Theme']);
        document.getElementById('settingsBtn').setAttribute('title', t['Settings']);
        document.getElementById('fullscreenBtn').setAttribute('title', t['Toggle Fullscreen']);

        // Update input placeholder
        document.getElementById('urlInput').setAttribute('placeholder', t['Enter YouTube URL or search...']);

        // Update settings modal content
        document.querySelector('.modal-header h2').textContent = t['Settings'];

        // Update section headers
        const sections = document.querySelectorAll('.settings-section h3');
        if (sections[0]) sections[0].textContent = t['Video Quality'];
        if (sections[1]) sections[1].textContent = t['Player Settings'];
        if (sections[2]) sections[2].textContent = t['Appearance'];

        // Update quality labels
        document.querySelectorAll('.quality-label').forEach(label => {
            const value = label.textContent.trim();
            if (value === 'Auto' || value === 'تلقائي') {
                label.textContent = t['Auto'];
            }
        });

        // Update setting labels
        document.querySelector('label[for="autoplay"]').textContent = t['Autoplay'];
        document.querySelector('label[for="volume"]').textContent = t['Default Volume'];
        document.querySelector('label[for="theme"]').textContent = t['Theme'];
        document.querySelector('label[for="language"]').textContent = t['Language'];

        // Update theme options
        const themeOptions = document.querySelectorAll('#theme option');
        if (themeOptions[0]) themeOptions[0].textContent = t['Dark'];
        if (themeOptions[1]) themeOptions[1].textContent = t['Light'];
        if (themeOptions[2]) themeOptions[2].textContent = t['Auto'];

        // Update language options
        const languageOptions = document.querySelectorAll('#language option');
        if (languageOptions[0]) languageOptions[0].textContent = t['Arabic'];
        if (languageOptions[1]) languageOptions[1].textContent = t['English'];

        // Update buttons
        document.getElementById('saveSettingsBtn').textContent = t['Save Settings'];
        document.getElementById('cancelSettingsBtn').textContent = t['Cancel'];

        // Update quality overlay
        document.querySelector('.quality-panel h3').textContent = t['Video Quality'];
        document.querySelectorAll('.quality-btn').forEach(btn => {
            const quality = btn.dataset.quality;
            if (quality === 'auto') {
                btn.textContent = t['Auto'];
            }
        });

        // Update loading indicator
        const loadingText = document.querySelector('#loadingIndicator span');
        if (loadingText) loadingText.textContent = t['Loading...'];
    }

    applyLanguageToYouTube(language) {
        // Prevent recursive language applications
        if (this.isApplyingLanguage || this.lastAppliedLanguage === language) {
            return;
        }

        if (!this.webview) {
            return;
        }

        // Set flag to prevent recursive calls
        this.isApplyingLanguage = true;
        this.lastAppliedLanguage = language;

        // Try to apply language with a single attempt
        this.performLanguageApplication(language);
    }

    performLanguageApplication(language) {
        let currentUrl;
        try {
            currentUrl = this.webview.getURL();
        } catch (error) {
            // Webview not ready, try once more after delay
            setTimeout(() => {
                this.performLanguageApplication(language);
            }, 1500);
            return;
        }

        // If URL is empty or not loaded yet, wait once more
        if (!currentUrl || currentUrl === 'about:blank' || currentUrl === '') {
            setTimeout(() => {
                this.performLanguageApplication(language);
            }, 1500);
            return;
        }

        // Apply language to YouTube URL
        if (currentUrl.includes('youtube.com')) {
            const modifiedUrl = this.addLanguageParameterToUrl(currentUrl, language);

            if (modifiedUrl !== currentUrl) {
                // Show notification
                const message = language === 'ar' ?
                    'تطبيق اللغة العربية على يوتيوب...' :
                    'Applying English language to YouTube...';
                this.showNotification(message, 'info');

                // Navigate to URL with language parameter
                this.webview.loadURL(modifiedUrl);

                // Reset flag after navigation
                setTimeout(() => {
                    this.isApplyingLanguage = false;
                }, 3000);
            } else {
                // URL already correct, reset flag immediately
                this.isApplyingLanguage = false;
            }
        } else {
            // Not a YouTube URL, reset flag
            this.isApplyingLanguage = false;
        }
    }

    addLanguageParameterToUrl(url, language) {
        try {
            const urlObj = new URL(url);
            urlObj.searchParams.set('hl', language);
            return urlObj.toString();
        } catch (error) {
            return url;
        }
    }

    ensureYouTubeLanguage(language) {
        // Don't interfere if language is already being applied
        if (this.isApplyingLanguage) {
            return;
        }

        if (!this.webview) return;

        try {
            const currentUrl = this.webview.getURL();

            if (currentUrl && currentUrl.includes('youtube.com')) {
                // Check if the URL already has the correct language parameter
                const urlObj = new URL(currentUrl);
                const currentLang = urlObj.searchParams.get('hl');

                // Only apply if language is significantly different and we're not already applying
                if (currentLang !== language && !this.isApplyingLanguage) {
                    // Use the main language application method to maintain consistency
                    this.applyLanguageToYouTube(language);
                }
            }
        } catch (error) {
            // Silent fail
        }
    }

    injectYouTubeLanguage(language) {
        if (!this.webview) return;

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;

            const jsCode = `
                (function() {
                    try {
                        // Set document language
                        document.documentElement.lang = '${language}';

                        // Set language preference in localStorage
                        try {
                            localStorage.setItem('yt-player-language', '${language}');
                            localStorage.setItem('yt-language', '${language}');
                        } catch (e) {
                            // localStorage might not be available
                        }

                        // Check current URL and add language parameter if missing
                        const currentUrl = window.location.href;

                        if (!currentUrl.includes('hl=${language}')) {
                            const url = new URL(currentUrl);
                            url.searchParams.set('hl', '${language}');

                            // Navigate to URL with language parameter
                            if (url.toString() !== currentUrl) {
                                window.location.href = url.toString();
                                return;
                            }
                        }

                    } catch (error) {
                        // Silent fail
                    }
                })();
            `;

            webContents.executeJavaScript(jsCode).catch(() => {
                // Silent fail
            });

        } catch (error) {
            // Silent fail
        }
    }

    applyThemeToWebview(theme) {
        // Apply theme immediately
        this.applyThemeImmediate(theme);

        // Retry with delays to ensure application
        setTimeout(() => this.applyThemeImmediate(theme), 100);
        setTimeout(() => this.applyThemeImmediate(theme), 500);
        setTimeout(() => this.applyThemeImmediate(theme), 1000);

        // Fallback DOM manipulation
        setTimeout(() => this.forceThemeViaDOM(theme), 200);
        setTimeout(() => this.forceThemeViaDOM(theme), 1000);
    }
    
    applyThemeImmediate(theme) {
        if (!this.webview) return;

        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;

            // Remove any previously injected theme CSS
            if (this.injectedThemeKey) {
                webContents.removeInsertedCSS(this.injectedThemeKey).catch(() => {});
            }

            // Create CSS for the selected theme
            const themeCSS = this.generateThemeCSS(theme);

            // Inject the CSS
            webContents.insertCSS(themeCSS, { cssOrigin: 'author' })
                .then(key => {
                    this.injectedThemeKey = key;
                    // Also inject via JavaScript for immediate effect
                    this.injectThemeViaJavaScript(theme);
                })
                .catch(() => {
                    // Try JavaScript injection as fallback
                    this.injectThemeViaJavaScript(theme);
                    this.forceThemeViaDOM(theme);
                });

        } catch (error) {
            // Try JavaScript injection as fallback
            this.injectThemeViaJavaScript(theme);
            this.forceThemeViaDOM(theme);
        }
    }
    
    injectThemeViaJavaScript(theme) {
        if (!this.webview) return;
        
        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;
            
            const jsCode = `
                (function() {
                    try {
                        // Remove any existing theme styles
                        const existingStyles = document.querySelectorAll('style[data-youtube-theme]');
                        existingStyles.forEach(style => style.remove());
                        
                        // Create new style element
                        const style = document.createElement('style');
                        style.setAttribute('data-youtube-theme', '${theme}');
                        style.textContent = \`${this.generateThemeCSS(theme)}\`;
                        
                        // Insert at the beginning of head for highest priority
                        document.head.insertBefore(style, document.head.firstChild);
                        
                        // Force immediate repaint
                        document.body.style.display = 'none';
                        document.body.offsetHeight; // Force reflow
                        document.body.style.display = '';
                        
                        // Also apply theme directly to key elements
                        const keyElements = [
                            'html', 'body', '#page-manager', '#content', '#primary', 
                            '#secondary', 'ytd-masthead', '#masthead-container'
                        ];
                        
                        keyElements.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                if ('${theme}' === 'dark') {
                                    el.style.backgroundColor = '#0f0f0f';
                                    el.style.color = '#ffffff';
                                } else {
                                    el.style.backgroundColor = '#ffffff';
                                    el.style.color = '#0f0f0f';
                                }
                            });
                        });
                        
                    } catch (error) {
                        console.error('Error applying theme via JavaScript:', error);
                    }
                })();
            `;

            webContents.executeJavaScript(jsCode).catch(() => {});

        } catch (error) {
            // Silent fail
        }
    }
    
    forceThemeViaDOM(theme) {
        if (!this.webview) return;
        
        try {
            const webContents = this.webview.getWebContents();
            if (!webContents) return;
            
            const jsCode = `
                (function() {
                    try {
                        const isDark = '${theme}' === 'dark';
                        
                        // Force theme on root elements first
                        document.documentElement.style.setProperty('background-color', isDark ? '#0f0f0f' : '#ffffff', 'important');
                        document.documentElement.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                        document.body.style.setProperty('background-color', isDark ? '#0f0f0f' : '#ffffff', 'important');
                        document.body.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                        
                        // Target YouTube-specific elements
                        const youtubeSelectors = [
                            '#page-manager', '#content', '#primary', '#secondary',
                            'ytd-app', 'ytd-page-manager', 'ytd-browse', 'ytd-watch',
                            'ytd-masthead', '#masthead-container', '#header',
                            'ytd-guide-renderer', 'ytd-guide-section-renderer',
                            'ytd-video-renderer', 'ytd-compact-video-renderer',
                            'ytd-rich-item-renderer', 'ytd-thumbnail'
                        ];
                        
                        youtubeSelectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                el.style.setProperty('background-color', isDark ? '#0f0f0f' : '#ffffff', 'important');
                                el.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                            });
                        });
                        
                        // Force theme on all text elements
                        const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, a, yt-formatted-string');
                        textElements.forEach(el => {
                            el.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                        });
                        
                        // Force theme on input elements
                        const inputElements = document.querySelectorAll('input, textarea, select, #search-input');
                        inputElements.forEach(el => {
                            el.style.setProperty('background-color', isDark ? '#2a2a2a' : '#ffffff', 'important');
                            el.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                            el.style.setProperty('border-color', isDark ? '#444444' : '#cccccc', 'important');
                        });
                        
                        // Force theme on buttons
                        const buttonElements = document.querySelectorAll('button, .ytp-button, ytd-button-renderer');
                        buttonElements.forEach(el => {
                            el.style.setProperty('background-color', isDark ? '#4a4a4a' : '#f8f9fa', 'important');
                            el.style.setProperty('color', isDark ? '#ffffff' : '#0f0f0f', 'important');
                        });
                        
                        // Special handling for media elements - keep them transparent
                        const mediaElements = document.querySelectorAll('img, video, .ytp-pause-overlay, .ytp-cued-thumbnail-overlay');
                        mediaElements.forEach(el => {
                            el.style.setProperty('background-color', 'transparent', 'important');
                            el.style.setProperty('filter', 'none', 'important');
                        });
                        
                        // Force repaint
                        document.body.style.display = 'none';
                        document.body.offsetHeight; // Force reflow
                        document.body.style.display = '';
                        
                        console.log('Theme forced via enhanced DOM manipulation: ${theme}');
                    } catch (error) {
                        console.error('Error in enhanced DOM theme forcing:', error);
                    }
                })();
            `;
            
            webContents.executeJavaScript(jsCode).catch(err => {
                console.log('⚠️ Enhanced DOM theme forcing failed:', err.message);
            });
            
        } catch (error) {
            console.log('⚠️ Error in enhanced DOM theme forcing:', error.message);
        }
    }
    


    
    generateThemeCSS(theme) {
        // YouTube-specific dark theme CSS with ultra-aggressive selectors
        const darkThemeCSS = `
            /* YouTube Dark Theme - Ultra Aggressive Override */
            
            /* Force dark theme on ALL elements with maximum specificity */
            *, *::before, *::after {
                background-color: #0f0f0f !important;
                color: #ffffff !important;
                border-color: #333333 !important;
            }
            
            /* Root elements with highest priority */
            html, body {
                background-color: #0f0f0f !important;
                color: #ffffff !important;
            }
            
            /* YouTube-specific elements with maximum specificity */
            #page-manager, #content, #primary, #secondary, #guide-content, #guide-renderer,
            ytd-masthead, #masthead-container, #header, ytd-guide-section-renderer,
            ytd-watch-flexy, ytd-video-primary-info-renderer, ytd-video-secondary-info-renderer,
            ytd-comments, #comments, ytd-video-renderer, ytd-compact-video-renderer, ytd-rich-item-renderer,
            ytd-app, ytd-page-manager, ytd-browse, ytd-watch, ytd-search,
            #container, #content-container, #content-wrapper, #page-container {
                background-color: #0f0f0f !important;
                color: #ffffff !important;
            }
            
            /* Text elements with forced white color */
            h1, h2, h3, h4, h5, h6, p, span, div, a, yt-formatted-string, 
            .title, .ytd-video-primary-info-renderer, .ytd-video-secondary-info-renderer,
            .ytd-video-title, .ytd-video-meta-block, .ytd-channel-name,
            .ytd-video-secondary-info-renderer, .ytd-video-primary-info-renderer {
                color: #ffffff !important;
            }
            
            /* Input fields and search with dark background */
            input, textarea, select, #search-input, #search-form, 
            ytd-searchbox, #search-container, #search-form-container,
            ytd-searchbox input, #search-input input, #search-form input {
                background-color: #2a2a2a !important;
                color: #ffffff !important;
                border-color: #444444 !important;
            }
            
            /* Buttons with dark styling */
            button, .ytp-button, ytd-button-renderer, 
            ytd-toggle-button-renderer, ytd-button-renderer,
            ytd-button-renderer button, ytd-toggle-button-renderer button {
                background-color: #4a4a4a !important;
                color: #ffffff !important;
            }
            
            /* Video player controls */
            .ytp-chrome-controls, .ytp-show-cards-title, .ytp-watermark,
            .ytp-pause-overlay, .ytp-cued-thumbnail-overlay,
            .ytp-player-content, .ytp-player-content video {
                background-color: rgba(0, 0, 0, 0.8) !important;
                color: #ffffff !important;
            }
            
            /* Navigation and sidebar elements */
            ytd-guide-renderer, ytd-guide-section-renderer, ytd-guide-entry-renderer,
            #guide-content, #guide-renderer, ytd-mini-guide-renderer,
            ytd-guide-entry-renderer a, ytd-guide-section-renderer a {
                background-color: #0f0f0f !important;
                color: #ffffff !important;
            }
            
            /* Video thumbnails and cards */
            ytd-thumbnail, ytd-video-renderer, ytd-compact-video-renderer,
            ytd-rich-item-renderer, ytd-rich-grid-media, ytd-rich-grid-row {
                background-color: #0f0f0f !important;
                color: #ffffff !important;
            }
            
            /* Exceptions for media content */
            img, video, .ytp-pause-overlay, .ytp-cued-thumbnail-overlay,
            .ytp-logo, .ytp-button, .ytp-menuitem-icon,
            .ytp-chrome-controls, .ytp-show-cards-title, .ytp-watermark,
            ytd-thumbnail img, ytd-video-renderer img, ytd-compact-video-renderer img {
                background-color: transparent !important;
                filter: none !important;
            }
            
            /* Force dark theme on any remaining elements */
            div, section, article, aside, nav, header, footer, main {
                background-color: #0f0f0f !important;
                color: #ffffff !important;
            }
            
            /* Override any YouTube theme classes */
            [class*="yt"], [class*="dark"], [class*="light"] {
                background-color: #0f0f0f !important;
                color: #ffffff !important;
            }
        `;
        
        const lightThemeCSS = `
            /* YouTube Light Theme - Ultra Aggressive Override */
            
            /* Force light theme on ALL elements with maximum specificity */
            *, *::before, *::after {
                background-color: #ffffff !important;
                color: #0f0f0f !important;
                border-color: #cccccc !important;
            }
            
            /* Root elements with highest priority */
            html, body {
                background-color: #ffffff !important;
                color: #0f0f0f !important;
            }
            
            /* YouTube-specific elements with maximum specificity */
            #page-manager, #content, #primary, #secondary, #guide-content, #guide-renderer,
            ytd-masthead, #masthead-container, #header, ytd-guide-section-renderer,
            ytd-watch-flexy, ytd-video-primary-info-renderer, ytd-video-secondary-info-renderer,
            ytd-comments, #comments, ytd-video-renderer, ytd-compact-video-renderer, ytd-rich-item-renderer,
            ytd-app, ytd-page-manager, ytd-browse, ytd-watch, ytd-search,
            #container, #content-container, #content-wrapper, #page-container {
                background-color: #ffffff !important;
                color: #0f0f0f !important;
            }
            
            /* Text elements with forced dark color */
            h1, h2, h3, h4, h5, h6, p, span, div, a, yt-formatted-string, 
            .title, .ytd-video-primary-info-renderer, .ytd-video-secondary-info-renderer,
            .ytd-video-title, .ytd-video-meta-block, .ytd-channel-name,
            .ytd-video-secondary-info-renderer, .ytd-video-primary-info-renderer {
                color: #0f0f0f !important;
            }
            
            /* Input fields and search with light background */
            input, textarea, select, #search-input, #search-form, 
            ytd-searchbox, #search-container, #search-form-container,
            ytd-searchbox input, #search-input input, #search-form input {
                background-color: #ffffff !important;
                color: #0f0f0f !important;
                border-color: #cccccc !important;
            }
            
            /* Buttons with light styling */
            button, .ytp-button, ytd-button-renderer, 
            ytd-toggle-button-renderer, ytd-button-renderer,
            ytd-button-renderer button, ytd-toggle-button-renderer button {
                background-color: #f8f9fa !important;
                color: #0f0f0f !important;
            }
            
            /* Video player controls */
            .ytp-chrome-controls, .ytp-show-cards-title, .ytp-watermark,
            .ytp-pause-overlay, .ytp-cued-thumbnail-overlay,
            .ytp-player-content, .ytp-player-content video {
                background-color: rgba(0, 0, 0, 0.8) !important;
                color: #ffffff !important;
            }
            
            /* Navigation and sidebar elements */
            ytd-guide-renderer, ytd-guide-section-renderer, ytd-guide-entry-renderer,
            #guide-content, #guide-renderer, ytd-mini-guide-renderer,
            ytd-guide-entry-renderer a, ytd-guide-section-renderer a {
                background-color: #ffffff !important;
                color: #0f0f0f !important;
            }
            
            /* Video thumbnails and cards */
            ytd-thumbnail, ytd-video-renderer, ytd-compact-video-renderer,
            ytd-rich-item-renderer, ytd-rich-grid-media, ytd-rich-grid-row {
                background-color: #ffffff !important;
                color: #0f0f0f !important;
            }
            
            /* Exceptions for media content */
            img, video, .ytp-pause-overlay, .ytp-cued-thumbnail-overlay,
            .ytp-logo, .ytp-button, .ytp-menuitem-icon,
            .ytp-chrome-controls, .ytp-show-cards-title, .ytp-watermark,
            ytd-thumbnail img, ytd-video-renderer img, ytd-compact-video-renderer img {
                background-color: transparent !important;
                filter: none !important;
            }
            
            /* Force light theme on any remaining elements */
            div, section, article, aside, nav, header, footer, main {
                background-color: #ffffff !important;
                color: #0f0f0f !important;
            }
            
            /* Override any YouTube theme classes */
            [class*="yt"], [class*="dark"], [class*="light"] {
                background-color: #ffffff !important;
                color: #0f0f0f !important;
            }
        `;
        
        // Auto theme CSS that responds to system preference
        const autoThemeCSS = `
            /* YouTube Auto Theme - follows system preference */
            @media (prefers-color-scheme: dark) {
                ${darkThemeCSS}
            }
            
            @media (prefers-color-scheme: light) {
                ${lightThemeCSS}
            }
        `;
        
        // Return appropriate CSS based on theme
        switch (theme) {
            case 'dark':
                return darkThemeCSS;
            case 'light':
                return lightThemeCSS;
            case 'auto':
                return autoThemeCSS;
            default:
                return darkThemeCSS;
        }
    }
    
    toggleTheme() {
        const currentTheme = this.currentSettings.theme || 'dark';
        let newTheme;
        
        if (currentTheme === 'dark') {
            newTheme = 'light';
        } else if (currentTheme === 'light') {
            newTheme = 'auto';
        } else {
            newTheme = 'dark';
        }
        
        console.log(`🎨 Toggling theme from ${currentTheme} to ${newTheme}`);
        this.applyTheme(newTheme);
        
        // Ultra-aggressive immediate application with better timing
        this.applyThemeToWebview(newTheme);
        setTimeout(() => this.applyThemeToWebview(newTheme), 5);
        setTimeout(() => this.applyThemeToWebview(newTheme), 10);
        setTimeout(() => this.applyThemeToWebview(newTheme), 25);
        setTimeout(() => this.applyThemeToWebview(newTheme), 50);
        setTimeout(() => this.applyThemeToWebview(newTheme), 100);
        setTimeout(() => this.applyThemeToWebview(newTheme), 200);
        setTimeout(() => this.applyThemeToWebview(newTheme), 500);
        setTimeout(() => this.applyThemeToWebview(newTheme), 1000);
        
        // Also try direct DOM manipulation - more aggressive
        setTimeout(() => this.forceThemeViaDOM(newTheme), 5);
        setTimeout(() => this.forceThemeViaDOM(newTheme), 10);
        setTimeout(() => this.forceThemeViaDOM(newTheme), 25);
        setTimeout(() => this.forceThemeViaDOM(newTheme), 50);
        setTimeout(() => this.forceThemeViaDOM(newTheme), 100);
        setTimeout(() => this.forceThemeViaDOM(newTheme), 200);
        setTimeout(() => this.forceThemeViaDOM(newTheme), 500);
        
        // Update theme select in settings
        if (this.themeSelect) {
            this.themeSelect.value = newTheme;
        }
    }
    
    updateThemeToggleIcon() {
        const currentTheme = this.currentSettings.theme || 'dark';
        const icon = this.themeToggleBtn.querySelector('i');
        
        if (currentTheme === 'light') {
            icon.className = 'fas fa-sun';
            this.themeToggleBtn.title = 'Switch to Auto Theme';
        } else if (currentTheme === 'auto') {
            icon.className = 'fas fa-adjust';
            this.themeToggleBtn.title = 'Switch to Dark Theme';
        } else {
            icon.className = 'fas fa-moon';
            this.themeToggleBtn.title = 'Switch to Light Theme';
        }
    }

    openSettings() {
        this.isSettingsOpen = true;
        this.settingsModal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // Focus first input for accessibility
        setTimeout(() => {
            const firstInput = this.settingsModal.querySelector('input, select');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    closeSettings() {
        this.isSettingsOpen = false;
        this.settingsModal.classList.remove('show');
        document.body.style.overflow = '';

        // Reset form to current settings
        this.updateSettingsUI();
    }

    async saveSettings() {
        try {
            const newSettings = {
                videoQuality: this.currentQuality,
                autoplay: this.autoplayInput.checked,
                volume: parseInt(this.volumeInput.value),
                theme: this.themeSelect.value,
                language: this.languageSelect.value
            };
            
            const result = await window.electronAPI.saveSettings(newSettings);
            
            if (result.success) {
                const oldSettings = this.currentSettings;
                this.currentSettings = newSettings;


                // Apply language if changed
                if (newSettings.language !== oldSettings.language) {
                    this.applyLanguage(newSettings.language);
                }

                // Apply quality to YouTube if changed
                if (newSettings.videoQuality !== oldSettings.videoQuality) {
                    await this.setVideoQuality(newSettings.videoQuality);
                    // Apply immediately to current video
                    setTimeout(() => {
                        this.applyQualityToYouTube();
                    }, 500);
                }

                this.closeSettings();
                const message = newSettings.language === 'ar' ? 'تم حفظ الإعدادات بنجاح!' : 'Settings saved successfully!';
                this.showNotification(message, 'success');
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showNotification('Failed to save settings', 'error');
        }
    }

    async setVideoQuality(quality) {
        try {
            const result = await window.electronAPI.setVideoQuality(quality);

            if (result.success) {
                this.currentQuality = quality;
                this.updateQualityDisplay();

                // Show feedback to user
                this.showNotification(`Applying ${quality} quality...`, 'info');

                // Apply quality with retry mechanism
                this.applyQualityToYouTube();

                // Show success message after a delay
                setTimeout(() => {
                    this.showNotification(`Quality set to ${quality}`, 'success');
                }, 2000);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('Failed to set video quality:', error);
            this.showNotification('Failed to set video quality', 'error');
        }
    }

    applyQualityToYouTube() {
        // Inject JavaScript to change YouTube video quality using enhanced DOM manipulation
        const qualityScript = `
            (function() {
                try {
                    console.log('Applying quality setting: ${this.currentQuality}');
                    
                    function waitForElement(selector, timeout = 10000) {
                        return new Promise((resolve, reject) => {
                            const element = document.querySelector(selector);
                            if (element) {
                                resolve(element);
                                return;
                            }
                            
                            const observer = new MutationObserver((mutations) => {
                                const element = document.querySelector(selector);
                                if (element) {
                                    observer.disconnect();
                                    resolve(element);
                                }
                            });
                            
                            observer.observe(document.body, {
                                childList: true,
                                subtree: true
                            });
                            
                            setTimeout(() => {
                                observer.disconnect();
                                reject(new Error('Element not found: ' + selector));
                            }, timeout);
                        });
                    }
                    
                    async function applyQualitySetting() {
                        try {
                            // Wait for video player to be ready
                            const player = await waitForElement('#movie_player');
                            console.log('Player found');
                            
                            // Wait for video to be loaded
                            const video = player.querySelector('video');
                            if (!video || video.readyState < 2) {
                                console.log('Video not ready, waiting...');
                                setTimeout(applyQualitySetting, 2000);
                                return;
                            }
                            
                            // Click settings button
                            const settingsButton = await waitForElement('.ytp-settings-button');
                            console.log('Settings button found');
                            settingsButton.click();
                            
                            // Wait for settings menu to appear
                            await new Promise(resolve => setTimeout(resolve, 300));
                            
                            // Look for quality menu item
                            const menuItems = document.querySelectorAll('.ytp-menuitem');
                            let qualityMenuItem = null;
                            
                            for (let item of menuItems) {
                                const text = item.textContent.toLowerCase();
                                if (text.includes('quality') || text.includes('144p') || text.includes('240p') || 
                                    text.includes('360p') || text.includes('480p') || text.includes('720p') || 
                                    text.includes('1080p') || text.includes('4k')) {
                                    qualityMenuItem = item;
                                    break;
                                }
                            }
                            
                            if (!qualityMenuItem) {
                                console.log('Quality menu item not found, trying alternative selectors');
                                // Try alternative selectors
                                const altSelectors = [
                                    '[aria-label*="quality"]',
                                    '[aria-label*="Quality"]',
                                    '.ytp-settings-menu .ytp-menuitem',
                                    '.ytp-panel-menu .ytp-menuitem'
                                ];
                                
                                for (let selector of altSelectors) {
                                    const items = document.querySelectorAll(selector);
                                    for (let item of items) {
                                        const text = item.textContent.toLowerCase();
                                        if (text.includes('quality') || text.includes('144p') || text.includes('240p') || 
                                            text.includes('360p') || text.includes('480p') || text.includes('720p') || 
                                            text.includes('1080p') || text.includes('4k')) {
                                            qualityMenuItem = item;
                                            break;
                                        }
                                    }
                                    if (qualityMenuItem) break;
                                }
                            }
                            
                            if (qualityMenuItem) {
                                console.log('Quality menu item found, clicking...');
                                qualityMenuItem.click();
                                
                                // Wait for quality submenu to appear
                                await new Promise(resolve => setTimeout(resolve, 300));
                                
                                // Find and click the target quality
                                const targetQuality = '${this.currentQuality}';
                                const qualityOptions = document.querySelectorAll('.ytp-menuitem');
                                let qualityFound = false;
                                
                                // Enhanced quality mapping
                                const qualityMap = {
                                    '144p': ['144p', 'tiny', '144'],
                                    '240p': ['240p', 'small', '240'],
                                    '360p': ['360p', 'medium', '360'],
                                    '480p': ['480p', 'large', '480'],
                                    '720p': ['720p', 'hd720', '720', 'hd'],
                                    '1080p': ['1080p', 'hd1080', '1080', 'full hd'],
                                    '4k': ['4k', 'hd1440', '2160p', '2160', 'ultra hd', 'uhd']
                                };
                                
                                const targetQualities = qualityMap[targetQuality] || [targetQuality];
                                
                                for (let option of qualityOptions) {
                                    const text = option.textContent.toLowerCase();
                                    console.log('Checking quality option:', text);
                                    
                                    for (let quality of targetQualities) {
                                        if (text.includes(quality.toLowerCase())) {
                                            console.log('Found matching quality option:', text);
                                            option.click();
                                            qualityFound = true;
                                            break;
                                        }
                                    }
                                    
                                    if (qualityFound) break;
                                }
                                
                                if (!qualityFound) {
                                    console.log('Target quality not found, trying auto');
                                    // Try to find auto quality as fallback
                                    for (let option of qualityOptions) {
                                        const text = option.textContent.toLowerCase();
                                        if (text.includes('auto') || text.includes('automatic')) {
                                            console.log('Found auto quality option');
                                            option.click();
                                            break;
                                        }
                                    }
                                }
                            } else {
                                console.log('Quality menu item not found');
                            }
                            
                            // Close settings menu
                            setTimeout(() => {
                                const closeButton = document.querySelector('.ytp-settings-button');
                                if (closeButton) {
                                    closeButton.click();
                                }
                            }, 500);
                            
                        } catch (error) {
                            console.error('Error in applyQualitySetting:', error);
                            // Retry after a delay
                            setTimeout(applyQualitySetting, 3000);
                        }
                    }
                    
                    // Try multiple methods to apply quality
                    async function tryAllQualityMethods() {
                        // Method 1: Enhanced DOM manipulation
                        try {
                            await applyQualitySetting();
                        } catch (error) {
                            console.log('DOM method failed, trying alternative methods');
                            
                            // Method 2: Try to use YouTube's internal API if available
                            try {
                                const player = document.querySelector('#movie_player');
                                if (player && player.getPlayerState) {
                                    const targetQuality = '${this.currentQuality}';
                                    console.log('Trying YouTube internal API for quality:', targetQuality);
                                    
                                    // Map quality to YouTube's internal format
                                    const qualityMap = {
                                        '144p': 'tiny',
                                        '240p': 'small', 
                                        '360p': 'medium',
                                        '480p': 'large',
                                        '720p': 'hd720',
                                        '1080p': 'hd1080',
                                        '4k': 'hd1440'
                                    };
                                    
                                    const youtubeQuality = qualityMap[targetQuality] || targetQuality;
                                    
                                    // Try to set quality via internal API
                                    if (player.setPlaybackQualityRange) {
                                        player.setPlaybackQualityRange(youtubeQuality, youtubeQuality);
                                        console.log('Quality set via internal API');
                                    }
                                }
                            } catch (apiError) {
                                console.log('Internal API method failed:', apiError);
                                
                                // Method 3: Try keyboard shortcuts
                                try {
                                    const targetQuality = '${this.currentQuality}';
                                    console.log('Trying keyboard shortcut method');
                                    
                                    // Press 'i' to open quality menu (YouTube shortcut)
                                    const keyEvent = new KeyboardEvent('keydown', {
                                        key: 'i',
                                        code: 'KeyI',
                                        keyCode: 73,
                                        which: 73,
                                        bubbles: true,
                                        cancelable: true
                                    });
                                    
                                    document.dispatchEvent(keyEvent);
                                    
                                    // Wait and try to select quality
                                    setTimeout(() => {
                                        const qualityOptions = document.querySelectorAll('.ytp-menuitem');
                                        const targetQualities = ['144p', '240p', '360p', '480p', '720p', '1080p', '4k'];
                                        
                                        for (let option of qualityOptions) {
                                            const text = option.textContent.toLowerCase();
                                            if (text.includes(targetQuality.toLowerCase())) {
                                                option.click();
                                                console.log('Quality selected via keyboard shortcut');
                                                break;
                                            }
                                        }
                                    }, 500);
                                    
                                } catch (keyboardError) {
                                    console.log('Keyboard shortcut method failed:', keyboardError);
                                }
                            }
                        }
                    }
                    
                    // Start the quality application process
                    tryAllQualityMethods();
                    
                } catch (error) {
                    console.error('Failed to apply quality to YouTube:', error);
                }
            })();
        `;
        
        this.webview.executeJavaScript(qualityScript);
    }

    openQualityOverlay() {
        console.log('Opening quality overlay');
        this.isQualityOverlayOpen = true;
        this.qualityOverlay.classList.add('show');
        this.updateQualityDisplay();
    }

    closeQualityOverlay() {
        console.log('Closing quality overlay');
        this.isQualityOverlayOpen = false;
        this.qualityOverlay.classList.remove('show');
    }

    // Navigation methods
    navigate() {
        let url = this.urlInput.value.trim();

        if (!url) return;

        // Handle YouTube URLs and search
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            if (url.includes('youtube.com') || url.includes('youtu.be')) {
                url = 'https://' + url;
            } else {
                // Treat as YouTube search
                url = `https://www.youtube.com/results?search_query=${encodeURIComponent(url)}`;
            }
        }

        // Add language parameter to YouTube URLs
        if (url.includes('youtube.com')) {
            const currentLanguage = this.currentSettings.language || 'ar';
            url = this.addLanguageParameterToUrl(url, currentLanguage);
        }

        this.webview.loadURL(url);
    }

    goBack() {
        if (this.webview.canGoBack()) {
            this.webview.goBack();
        }
    }

    goForward() {
        if (this.webview.canGoForward()) {
            this.webview.goForward();
        }
    }

    reload() {
        this.webview.reload();
    }

    toggleFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            document.documentElement.requestFullscreen();
        }
    }

    updateUrlBar(url) {
        this.urlInput.value = url;
    }

    // Loading indicator
    showLoading() {
        this.loadingIndicator.classList.add('show');
    }

    hideLoading() {
        this.loadingIndicator.classList.remove('show');
    }

    // Keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + , to open settings
        if ((e.ctrlKey || e.metaKey) && e.key === ',') {
            e.preventDefault();
            this.openSettings();
        }
        
        // Ctrl/Cmd + R to reload
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            this.reload();
        }
        
        // Ctrl/Cmd + L to focus URL bar
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            this.urlInput.focus();
            this.urlInput.select();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            if (this.isSettingsOpen) {
                this.closeSettings();
            } else if (this.isQualityOverlayOpen) {
                this.closeQualityOverlay();
            }
        }
        
        // Ctrl/Cmd + T to toggle theme
        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
            e.preventDefault();
            this.toggleTheme();
        }
    }

    // Set up quality monitoring to apply settings to new videos
    setupQualityMonitoring() {
        const monitoringScript = `
            (function() {
                let lastUrl = '';
                let qualityApplied = false;
                let retryCount = 0;
                const maxRetries = 5;
                
                // Monitor for URL changes (new videos)
                function checkForVideoChanges() {
                    const currentUrl = window.location.href;
                    if (currentUrl !== lastUrl && currentUrl.includes('/watch')) {
                        console.log('New video detected:', currentUrl);
                        lastUrl = currentUrl;
                        qualityApplied = false;
                        retryCount = 0;
                        
                        // Wait for video to load and apply quality
                        setTimeout(() => {
                            if (!qualityApplied) {
                                console.log('Applying quality to new video');
                                window.postMessage({ type: 'APPLY_QUALITY' }, '*');
                                qualityApplied = true;
                            }
                        }, 3000);
                    }
                }
                
                // Monitor for video player state changes
                function monitorVideoPlayer() {
                    const player = document.querySelector('#movie_player');
                    if (player) {
                        // Check if video is playing
                        const video = player.querySelector('video');
                        if (video && !video.paused && video.currentTime > 0) {
                            if (!qualityApplied && retryCount < maxRetries) {
                                console.log('Video is playing, applying quality (attempt ' + (retryCount + 1) + ')');
                                setTimeout(() => {
                                    window.postMessage({ type: 'APPLY_QUALITY' }, '*');
                                    retryCount++;
                                    if (retryCount >= maxRetries) {
                                        qualityApplied = true;
                                    }
                                }, 1000);
                            }
                        }
                    }
                }
                
                // Monitor for settings button availability
                function monitorSettingsButton() {
                    const settingsButton = document.querySelector('.ytp-settings-button');
                    if (settingsButton && !qualityApplied && retryCount < maxRetries) {
                        console.log('Settings button available, applying quality');
                        setTimeout(() => {
                            window.postMessage({ type: 'APPLY_QUALITY' }, '*');
                            retryCount++;
                            if (retryCount >= maxRetries) {
                                qualityApplied = true;
                            }
                        }, 500);
                    }
                }
                
                // Set up monitoring with different intervals
                setInterval(checkForVideoChanges, 1000);
                setInterval(monitorVideoPlayer, 2000);
                setInterval(monitorSettingsButton, 1500);
                
                console.log('Enhanced quality monitoring set up');
            })();
        `;
        
        this.webview.executeJavaScript(monitoringScript);
        
        // Listen for quality application messages from the webview
        this.webview.addEventListener('ipc-message', (event) => {
            if (event.channel.type === 'APPLY_QUALITY') {
                console.log('Received quality application request from webview');
                this.applyQualityToYouTube();
            }
        });
    }
    
    // Test webview functionality
    testWebviewFunctionality() {
        try {
            console.log('Testing webview functionality...');
            
            // Check if webview element exists
            if (!this.webview) {
                console.error('Webview element not found');
                this.showNotification('Webview element not found', 'error');
                return;
            }
            
            // Check if webview is attached
            if (!this.webview.getWebContentsId) {
                console.error('Webview not properly attached');
                this.showNotification('Webview not properly attached', 'error');
                return;
            }
            
            console.log('Webview functionality test passed');
            this.showNotification('YouTube player loaded successfully', 'success');
            
        } catch (error) {
            console.error('Webview functionality test failed:', error);
            this.showNotification('Webview functionality test failed', 'error');
        }
    }
    
    // Notification system
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Get theme-aware colors
        const colors = {
            error: getComputedStyle(document.body).getPropertyValue('--error-color').trim() || '#ff4444',
            success: getComputedStyle(document.body).getPropertyValue('--success-color').trim() || '#44ff44',
            info: getComputedStyle(document.body).getPropertyValue('--info-color').trim() || '#4444ff'
        };
        
        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: var(--text-primary);
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 4px 12px var(--shadow-color);
            animation: slideIn 0.3s ease-out;
            border: 1px solid var(--border-color);
        `;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    


}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Add CSS animations for notifications
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // Create and initialize the player
    window.youtubePlayer = new YouTubePlayer();
});

// Handle any unhandled errors
window.addEventListener('error', (e) => {
    console.error('Unhandled error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
});

