/**
 * Theme Integration Layer
 * Seamless integration between ThemeManager and app settings
 */

class ThemeIntegration {
  constructor(options = {}) {
    this.options = {
      themeManager: null,
      appSettings: null,
      storageKey: 'youtube-app-theme-integration',
      syncInterval: 1000, // ms
      enableDebugLogging: false,
      ...options
    };

    this.themeManager = this.options.themeManager;
    this.appSettings = this.options.appSettings;
    this.syncTimer = null;
    this.isInitialized = false;
    this.lastKnownTheme = null;
    
    this.init();
  }

  /**
   * Initialize the integration
   */
  init() {
    try {
      if (!this.themeManager) {
        throw new Error('ThemeManager instance is required');
      }

      // Set up bidirectional sync
      this.setupThemeManagerListeners();
      this.setupAppSettingsListeners();
      
      // Initial sync
      this.performInitialSync();
      
      // Start periodic sync (fallback)
      this.startPeriodicSync();
      
      this.isInitialized = true;
      this.log('ThemeIntegration initialized successfully');
      
    } catch (error) {
      console.error('ThemeIntegration initialization failed:', error);
    }
  }

  /**
   * Set up theme manager listeners
   */
  setupThemeManagerListeners() {
    if (!this.themeManager) return;

    // Listen for theme changes from ThemeManager
    this.themeManager.addListener((newTheme, previousTheme, effectiveTheme) => {
      this.log(`ThemeManager changed: ${previousTheme} → ${newTheme} (effective: ${effectiveTheme})`);
      this.syncToAppSettings(newTheme);
    });

    // Listen for DOM events
    document.addEventListener('themechange', (event) => {
      const { theme, effectiveTheme } = event.detail;
      this.log(`DOM theme change event: ${theme} (effective: ${effectiveTheme})`);
      this.handleThemeChange(theme, effectiveTheme);
    });
  }

  /**
   * Set up app settings listeners
   */
  setupAppSettingsListeners() {
    if (!this.appSettings) {
      this.log('No app settings available - using localStorage fallback');
      this.setupLocalStorageListeners();
      return;
    }

    try {
      // Listen for app settings theme changes
      if (typeof this.appSettings.onThemeChange === 'function') {
        this.appSettings.onThemeChange((theme) => {
          this.log(`App settings changed to: ${theme}`);
          this.syncFromAppSettings(theme);
        });
      }

      // Listen for app settings ready event
      if (typeof this.appSettings.onReady === 'function') {
        this.appSettings.onReady(() => {
          this.log('App settings ready - performing sync');
          this.performInitialSync();
        });
      }

    } catch (error) {
      console.error('Failed to setup app settings listeners:', error);
      this.setupLocalStorageListeners();
    }
  }

  /**
   * Set up localStorage listeners as fallback
   */
  setupLocalStorageListeners() {
    window.addEventListener('storage', (event) => {
      if (event.key === this.options.storageKey && event.newValue) {
        try {
          const data = JSON.parse(event.newValue);
          if (data.theme && data.theme !== this.lastKnownTheme) {
            this.log(`Storage sync: ${data.theme}`);
            this.syncFromStorage(data.theme);
          }
        } catch (error) {
          console.error('Failed to parse storage data:', error);
        }
      }
    });
  }

  /**
   * Perform initial sync between systems
   */
  async performInitialSync() {
    try {
      let appTheme = null;
      let managerTheme = this.themeManager?.getTheme();

      // Get theme from app settings (async)
      if (this.appSettings?.getTheme) {
        try {
          appTheme = await this.appSettings.getTheme();
        } catch (error) {
          console.warn('Failed to get theme from app settings:', error);
        }
      }

      // Fallback to localStorage if app settings unavailable
      if (!appTheme) {
        try {
          const stored = localStorage.getItem(this.options.storageKey);
          if (stored) {
            const data = JSON.parse(stored);
            appTheme = data.theme;
          }
        } catch (error) {
          console.warn('Failed to get theme from localStorage:', error);
        }
      }

      this.log(`Initial sync - App: ${appTheme}, Manager: ${managerTheme}`);

      // Determine which theme to use (prioritize app settings)
      let targetTheme = appTheme || managerTheme || 'auto';

      // Validate theme
      if (!['light', 'dark', 'auto'].includes(targetTheme)) {
        this.log(`Invalid theme detected: ${targetTheme}, falling back to auto`);
        targetTheme = 'auto';
      }

      // Apply the theme
      if (this.themeManager && targetTheme !== managerTheme) {
        this.themeManager.setTheme(targetTheme, { skipAppSync: true });
      }

      this.lastKnownTheme = targetTheme;

      // Ensure app settings are in sync
      if (appTheme !== targetTheme && this.appSettings?.setTheme) {
        try {
          await this.appSettings.setTheme(targetTheme);
        } catch (error) {
          console.warn('Failed to sync theme to app settings:', error);
        }
      }

    } catch (error) {
      console.error('Initial sync failed:', error);
      // Fallback to auto theme
      if (this.themeManager) {
        this.themeManager.setTheme('auto', { skipAppSync: true });
      }
    }
  }

  /**
   * Sync theme to app settings
   */
  syncToAppSettings(theme) {
    if (!theme || theme === this.lastKnownTheme) return;

    try {
      // Sync to app settings if available
      if (this.appSettings?.setTheme) {
        this.appSettings.setTheme(theme);
        this.log(`Synced to app settings: ${theme}`);
      } else {
        // Fallback to localStorage
        this.syncToStorage(theme);
      }

      this.lastKnownTheme = theme;

    } catch (error) {
      console.error('Failed to sync to app settings:', error);
    }
  }

  /**
   * Sync theme from app settings
   */
  syncFromAppSettings(theme) {
    if (!theme || theme === this.lastKnownTheme) return;

    try {
      if (this.themeManager) {
        this.themeManager.setTheme(theme, { skipAppSync: true });
        this.log(`Synced from app settings: ${theme}`);
      }

      this.lastKnownTheme = theme;

    } catch (error) {
      console.error('Failed to sync from app settings:', error);
    }
  }

  /**
   * Sync to localStorage (fallback)
   */
  syncToStorage(theme) {
    try {
      const data = {
        theme,
        timestamp: Date.now(),
        source: 'theme-integration'
      };
      localStorage.setItem(this.options.storageKey, JSON.stringify(data));
      this.log(`Synced to storage: ${theme}`);
    } catch (error) {
      console.error('Failed to sync to storage:', error);
    }
  }

  /**
   * Sync from localStorage (fallback)
   */
  syncFromStorage(theme) {
    if (!theme || theme === this.lastKnownTheme) return;

    try {
      if (this.themeManager) {
        this.themeManager.setTheme(theme, { skipAppSync: true });
        this.log(`Synced from storage: ${theme}`);
      }

      this.lastKnownTheme = theme;

    } catch (error) {
      console.error('Failed to sync from storage:', error);
    }
  }

  /**
   * Handle theme change events
   */
  handleThemeChange(theme, effectiveTheme) {
    try {
      // Update UI components that need manual updates
      this.updateCustomComponents(theme, effectiveTheme);
      
      // Notify external systems
      this.notifyExternalSystems(theme, effectiveTheme);
      
    } catch (error) {
      console.error('Failed to handle theme change:', error);
    }
  }

  /**
   * Update custom components that don't use CSS variables
   */
  updateCustomComponents(theme, effectiveTheme) {
    try {
      // Update webview theme
      this.updateWebviewTheme(effectiveTheme);
      
      // Update settings modal theme selector
      this.updateSettingsThemeSelector(theme);
      
    } catch (error) {
      console.error('Failed to update custom components:', error);
    }
  }

  /**
   * Update webview theme
   */
  updateWebviewTheme(effectiveTheme) {
    try {
      const webview = document.getElementById('webview');
      if (webview) {
        // YouTube will handle its own theme based on URL parameters
        // We can inject CSS or send messages if needed
        this.log(`Webview theme updated to: ${effectiveTheme}`);
      }
    } catch (error) {
      console.error('Failed to update webview theme:', error);
    }
  }

  /**
   * Update settings modal theme selector
   */
  updateSettingsThemeSelector(theme) {
    try {
      const themeSelect = document.getElementById('theme');
      if (themeSelect && themeSelect.value !== theme) {
        themeSelect.value = theme;
        
        // Update description
        const description = document.getElementById('themeDescription');
        if (description) {
          const descriptions = {
            'light': 'Light theme with bright colors',
            'dark': 'Dark theme with muted colors',
            'auto': 'Follows your system preference'
          };
          description.textContent = descriptions[theme] || descriptions.auto;
        }
      }
    } catch (error) {
      console.error('Failed to update settings theme selector:', error);
    }
  }

  /**
   * Notify external systems
   */
  notifyExternalSystems(theme, effectiveTheme) {
    try {
      // Notify main process via IPC
      if (window.electronAPI && window.electronAPI.themeChanged) {
        window.electronAPI.themeChanged(theme, effectiveTheme);
      }

    } catch (error) {
      console.error('Failed to notify external systems:', error);
    }
  }

  /**
   * Start periodic sync (fallback mechanism)
   */
  startPeriodicSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(() => {
      try {
        this.checkForThemeChanges();
      } catch (error) {
        console.error('Periodic sync error:', error);
      }
    }, this.options.syncInterval);
  }

  /**
   * Check for theme changes (fallback)
   */
  checkForThemeChanges() {
    try {
      let currentTheme = null;

      if (this.appSettings?.getTheme) {
        currentTheme = this.appSettings.getTheme();
      } else {
        const stored = localStorage.getItem(this.options.storageKey);
        if (stored) {
          const data = JSON.parse(stored);
          currentTheme = data.theme;
        }
      }

      if (currentTheme && currentTheme !== this.lastKnownTheme) {
        this.log(`Detected theme change via periodic sync: ${currentTheme}`);
        this.syncFromAppSettings(currentTheme);
      }

    } catch (error) {
      // Silent fail for periodic checks
    }
  }

  /**
   * Get current integration status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasThemeManager: !!this.themeManager,
      hasAppSettings: !!this.appSettings,
      lastKnownTheme: this.lastKnownTheme,
      currentTheme: this.themeManager?.getTheme(),
      effectiveTheme: this.themeManager?.getEffectiveTheme()
    };
  }

  /**
   * Debug logging
   */
  log(message) {
    if (this.options.enableDebugLogging) {
      console.log(`[ThemeIntegration] ${message}`);
    }
  }

  /**
   * Destroy the integration
   */
  destroy() {
    try {
      if (this.syncTimer) {
        clearInterval(this.syncTimer);
        this.syncTimer = null;
      }

      this.isInitialized = false;
      this.log('ThemeIntegration destroyed');

    } catch (error) {
      console.error('Failed to destroy ThemeIntegration:', error);
    }
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeIntegration;
} else if (typeof window !== 'undefined') {
  window.ThemeIntegration = ThemeIntegration;
}
