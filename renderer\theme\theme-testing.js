/**
 * Theme System Testing and Validation
 * Comprehensive testing suite for the YouTube-style theme system
 */

class ThemeSystemTester {
  constructor(themeManager, themeIntegration) {
    this.themeManager = themeManager;
    this.themeIntegration = themeIntegration;
    this.testResults = [];
    this.isRunning = false;
  }

  /**
   * Run all theme system tests
   */
  async runAllTests() {
    if (this.isRunning) {
      console.warn('Tests already running');
      return;
    }

    this.isRunning = true;
    this.testResults = [];
    
    console.log('🧪 Starting comprehensive theme system tests...');
    
    try {
      // Core functionality tests
      await this.testThemeManagerInitialization();
      await this.testThemeSwitching();
      await this.testSystemThemeDetection();
      await this.testStoragePersistence();
      
      // Integration tests
      await this.testAppSettingsIntegration();
      await this.testCrossTabSync();
      await this.testIPCCommunication();
      
      // UI tests
      await this.testCSSVariableApplication();
      await this.testUIComponentUpdates();
      await this.testTransitions();
      
      // Error handling tests
      await this.testErrorRecovery();
      await this.testFallbackMechanisms();
      
      // Performance tests
      await this.testPerformance();
      
      this.generateTestReport();
      
    } catch (error) {
      console.error('Test suite failed:', error);
      this.addTestResult('Test Suite', false, `Suite failed: ${error.message}`);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Test theme manager initialization
   */
  async testThemeManagerInitialization() {
    const testName = 'Theme Manager Initialization';
    
    try {
      // Check if theme manager exists
      if (!this.themeManager) {
        throw new Error('ThemeManager not found');
      }
      
      // Check if initialized
      if (!this.themeManager.isInitialized) {
        throw new Error('ThemeManager not initialized');
      }
      
      // Check initial theme
      const currentTheme = this.themeManager.getTheme();
      if (!['light', 'dark', 'auto'].includes(currentTheme)) {
        throw new Error(`Invalid initial theme: ${currentTheme}`);
      }
      
      this.addTestResult(testName, true, `Initialized with theme: ${currentTheme}`);
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test theme switching functionality
   */
  async testThemeSwitching() {
    const testName = 'Theme Switching';
    
    try {
      const originalTheme = this.themeManager.getTheme();
      const testThemes = ['light', 'dark', 'auto'];
      
      for (const theme of testThemes) {
        // Set theme
        const success = this.themeManager.setTheme(theme);
        if (!success) {
          throw new Error(`Failed to set theme: ${theme}`);
        }
        
        // Verify theme was set
        const currentTheme = this.themeManager.getTheme();
        if (currentTheme !== theme) {
          throw new Error(`Theme not set correctly. Expected: ${theme}, Got: ${currentTheme}`);
        }
        
        // Wait for DOM updates
        await this.wait(100);
        
        // Verify DOM attributes
        const htmlTheme = document.documentElement.getAttribute('data-theme');
        if (theme !== 'auto' && htmlTheme !== theme) {
          throw new Error(`DOM not updated correctly for theme: ${theme}`);
        }
      }
      
      // Restore original theme
      this.themeManager.setTheme(originalTheme);
      
      this.addTestResult(testName, true, 'All themes switched successfully');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test system theme detection
   */
  async testSystemThemeDetection() {
    const testName = 'System Theme Detection';
    
    try {
      const systemTheme = this.themeManager.getSystemTheme();
      
      if (!['light', 'dark'].includes(systemTheme)) {
        throw new Error(`Invalid system theme detected: ${systemTheme}`);
      }
      
      // Test auto theme resolution
      this.themeManager.setTheme('auto');
      const effectiveTheme = this.themeManager.getEffectiveTheme();
      
      if (effectiveTheme !== systemTheme) {
        throw new Error(`Auto theme not resolved correctly. Expected: ${systemTheme}, Got: ${effectiveTheme}`);
      }
      
      this.addTestResult(testName, true, `System theme: ${systemTheme}, Auto resolved: ${effectiveTheme}`);
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test storage persistence
   */
  async testStoragePersistence() {
    const testName = 'Storage Persistence';
    
    try {
      const testTheme = 'dark';
      const storageKey = this.themeManager.options.storageKey;
      
      // Set theme and verify storage
      this.themeManager.setTheme(testTheme);
      await this.wait(100);
      
      const storedTheme = localStorage.getItem(storageKey);
      if (storedTheme !== testTheme) {
        throw new Error(`Theme not persisted correctly. Expected: ${testTheme}, Stored: ${storedTheme}`);
      }
      
      // Test loading from storage
      const loadedTheme = this.themeManager.loadTheme();
      if (loadedTheme !== testTheme) {
        throw new Error(`Theme not loaded correctly. Expected: ${testTheme}, Loaded: ${loadedTheme}`);
      }
      
      this.addTestResult(testName, true, 'Storage persistence working correctly');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test app settings integration
   */
  async testAppSettingsIntegration() {
    const testName = 'App Settings Integration';
    
    try {
      if (!this.themeIntegration) {
        throw new Error('ThemeIntegration not found');
      }
      
      const status = this.themeIntegration.getStatus();
      
      if (!status.isInitialized) {
        throw new Error('ThemeIntegration not initialized');
      }
      
      if (!status.hasThemeManager) {
        throw new Error('ThemeManager not connected to integration');
      }
      
      this.addTestResult(testName, true, 'Integration working correctly');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test CSS variable application
   */
  async testCSSVariableApplication() {
    const testName = 'CSS Variable Application';
    
    try {
      // Test light theme variables
      this.themeManager.setTheme('light');
      await this.wait(100);
      
      const lightBg = getComputedStyle(document.documentElement).getPropertyValue('--theme-bg-primary').trim();
      if (!lightBg || lightBg === '') {
        throw new Error('CSS variables not applied for light theme');
      }
      
      // Test dark theme variables
      this.themeManager.setTheme('dark');
      await this.wait(100);
      
      const darkBg = getComputedStyle(document.documentElement).getPropertyValue('--theme-bg-primary').trim();
      if (!darkBg || darkBg === '' || darkBg === lightBg) {
        throw new Error('CSS variables not applied correctly for dark theme');
      }
      
      this.addTestResult(testName, true, 'CSS variables applied correctly');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test error recovery mechanisms
   */
  async testErrorRecovery() {
    const testName = 'Error Recovery';
    
    try {
      // Test invalid theme handling
      const result = this.themeManager.setTheme('invalid-theme');
      if (result) {
        throw new Error('Invalid theme was accepted');
      }
      
      // Verify fallback occurred
      const currentTheme = this.themeManager.getTheme();
      if (!['light', 'dark', 'auto'].includes(currentTheme)) {
        throw new Error('Fallback did not occur for invalid theme');
      }
      
      // Test system validation
      const validation = this.themeManager.validateThemeSystem();
      if (typeof validation.isValid !== 'boolean') {
        throw new Error('Theme system validation not working');
      }
      
      this.addTestResult(testName, true, 'Error recovery mechanisms working');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test performance
   */
  async testPerformance() {
    const testName = 'Performance';
    
    try {
      const iterations = 10;
      const startTime = performance.now();
      
      // Rapid theme switching
      for (let i = 0; i < iterations; i++) {
        this.themeManager.setTheme(i % 2 === 0 ? 'light' : 'dark');
        await this.wait(10);
      }
      
      const endTime = performance.now();
      const avgTime = (endTime - startTime) / iterations;
      
      if (avgTime > 50) { // 50ms threshold
        throw new Error(`Theme switching too slow: ${avgTime.toFixed(2)}ms average`);
      }
      
      this.addTestResult(testName, true, `Average switch time: ${avgTime.toFixed(2)}ms`);
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test cross-tab synchronization
   */
  async testCrossTabSync() {
    const testName = 'Cross-Tab Sync';
    
    try {
      // Simulate storage event
      const testTheme = 'light';
      const storageKey = this.themeManager.options.storageKey;
      
      // Trigger storage event
      const event = new StorageEvent('storage', {
        key: storageKey,
        newValue: testTheme,
        oldValue: 'dark'
      });
      
      window.dispatchEvent(event);
      await this.wait(100);
      
      // Verify theme was updated
      const currentTheme = this.themeManager.getTheme();
      if (currentTheme !== testTheme) {
        throw new Error('Cross-tab sync not working');
      }
      
      this.addTestResult(testName, true, 'Cross-tab sync working');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test IPC communication
   */
  async testIPCCommunication() {
    const testName = 'IPC Communication';
    
    try {
      if (!window.electronAPI) {
        this.addTestResult(testName, true, 'IPC not available (not in Electron)');
        return;
      }
      
      // Test theme retrieval
      const theme = await window.electronAPI.getTheme();
      if (!['light', 'dark', 'auto'].includes(theme)) {
        throw new Error(`Invalid theme from IPC: ${theme}`);
      }
      
      this.addTestResult(testName, true, 'IPC communication working');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test UI component updates
   */
  async testUIComponentUpdates() {
    const testName = 'UI Component Updates';
    
    try {
      // Test theme toggle button
      const themeToggleBtn = document.getElementById('themeToggleBtn');
      if (themeToggleBtn) {
        this.themeManager.setTheme('light');
        await this.wait(100);
        
        const icon = themeToggleBtn.querySelector('i');
        if (icon && !icon.className.includes('moon')) {
          throw new Error('Theme toggle icon not updated for light theme');
        }
      }
      
      this.addTestResult(testName, true, 'UI components updated correctly');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test transitions
   */
  async testTransitions() {
    const testName = 'Theme Transitions';
    
    try {
      // Check if transitions are enabled
      const bodyStyle = getComputedStyle(document.body);
      const transition = bodyStyle.transition;
      
      if (!transition.includes('background-color') && !transition.includes('color')) {
        throw new Error('Theme transitions not applied');
      }
      
      this.addTestResult(testName, true, 'Transitions working correctly');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Test fallback mechanisms
   */
  async testFallbackMechanisms() {
    const testName = 'Fallback Mechanisms';
    
    try {
      // Test system theme fallback
      this.themeManager.fallbackToSystemTheme();
      await this.wait(100);
      
      const currentTheme = this.themeManager.getTheme();
      if (currentTheme !== 'auto') {
        throw new Error('System theme fallback not working');
      }
      
      this.addTestResult(testName, true, 'Fallback mechanisms working');
      
    } catch (error) {
      this.addTestResult(testName, false, error.message);
    }
  }

  /**
   * Add test result
   */
  addTestResult(testName, passed, message) {
    const result = {
      name: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Generate comprehensive test report
   */
  generateTestReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log('\n📊 Theme System Test Report');
    console.log('================================');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${successRate}%`);
    console.log('================================\n');
    
    if (failedTests > 0) {
      console.log('❌ Failed Tests:');
      this.testResults.filter(r => !r.passed).forEach(result => {
        console.log(`  - ${result.name}: ${result.message}`);
      });
      console.log('');
    }
    
    // Show notification if available
    if (typeof window !== 'undefined' && window.showNotification) {
      const message = `Theme tests completed: ${passedTests}/${totalTests} passed (${successRate}%)`;
      const type = failedTests === 0 ? 'success' : 'warning';
      window.showNotification(message, type);
    }
    
    return {
      totalTests,
      passedTests,
      failedTests,
      successRate: parseFloat(successRate),
      results: this.testResults
    };
  }

  /**
   * Utility: Wait for specified time
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeSystemTester;
} else if (typeof window !== 'undefined') {
  window.ThemeSystemTester = ThemeSystemTester;
}
