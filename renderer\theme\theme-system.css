/**
 * YouTube-Style Theme System
 * Comprehensive CSS variable-based theming system
 */

:root {
  /* === BASE COLORS === */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f8f9fa;
  --theme-bg-tertiary: #e9ecef;
  --theme-bg-elevated: #ffffff;
  
  --theme-text-primary: #212529;
  --theme-text-secondary: #6c757d;
  --theme-text-tertiary: #adb5bd;
  --theme-text-inverse: #ffffff;
  
  --theme-border-primary: #dee2e6;
  --theme-border-secondary: #e9ecef;
  --theme-border-focus: #86b7fe;
  
  --theme-accent-primary: #0d6efd;
  --theme-accent-secondary: #6610f2;
  --theme-accent-success: #198754;
  --theme-accent-warning: #ffc107;
  --theme-accent-danger: #dc3545;
  --theme-accent-info: #0dcaf0;
  
  /* === INTERACTIVE STATES === */
  --theme-hover-bg: rgba(0, 0, 0, 0.04);
  --theme-active-bg: rgba(0, 0, 0, 0.08);
  --theme-focus-bg: rgba(13, 110, 253, 0.1);
  --theme-disabled-bg: #e9ecef;
  --theme-disabled-text: #6c757d;
  
  /* === SHADOWS AND EFFECTS === */
  --theme-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --theme-shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --theme-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --theme-overlay: rgba(0, 0, 0, 0.5);
  --theme-backdrop: rgba(255, 255, 255, 0.8);
  
  /* === COMPONENT SPECIFIC === */
  --theme-header-bg: var(--theme-bg-primary);
  --theme-header-border: var(--theme-border-primary);
  --theme-sidebar-bg: var(--theme-bg-secondary);
  --theme-card-bg: var(--theme-bg-primary);
  --theme-card-border: var(--theme-border-primary);
  --theme-input-bg: var(--theme-bg-primary);
  --theme-input-border: var(--theme-border-primary);
  --theme-button-bg: var(--theme-accent-primary);
  --theme-button-text: var(--theme-text-inverse);
  --theme-modal-bg: var(--theme-bg-primary);
  --theme-tooltip-bg: var(--theme-text-primary);
  --theme-tooltip-text: var(--theme-text-inverse);
  
  /* === TRANSITIONS === */
  --theme-transition-fast: 0.15s ease-in-out;
  --theme-transition-normal: 0.3s ease-in-out;
  --theme-transition-slow: 0.5s ease-in-out;

  /* === LEGACY COMPATIBILITY === */
  /* Map new theme variables to existing app variables */
  --bg-primary: var(--theme-bg-primary);
  --bg-secondary: var(--theme-bg-secondary);
  --bg-tertiary: var(--theme-bg-tertiary);
  --bg-quaternary: var(--theme-bg-tertiary);
  --text-primary: var(--theme-text-primary);
  --text-secondary: var(--theme-text-secondary);
  --text-muted: var(--theme-text-tertiary);
  --border-color: var(--theme-border-primary);
  --accent-color: var(--theme-accent-primary);
  --accent-hover: var(--theme-accent-primary);
  --shadow-color: var(--theme-shadow-md);
  --modal-bg: var(--theme-modal-bg);
  --input-bg: var(--theme-input-bg);
  --button-bg: var(--theme-button-bg);
  --button-hover: var(--theme-hover-bg);
  --success-color: var(--theme-accent-success);
  --error-color: var(--theme-accent-danger);
  --info-color: var(--theme-accent-info);
}

/* === DARK THEME === */
[data-theme="dark"] {
  /* Base colors */
  --theme-bg-primary: #0f0f0f;
  --theme-bg-secondary: #1a1a1a;
  --theme-bg-tertiary: #2a2a2a;
  --theme-bg-elevated: #1a1a1a;
  
  --theme-text-primary: #ffffff;
  --theme-text-secondary: #cccccc;
  --theme-text-tertiary: #888888;
  --theme-text-inverse: #000000;
  
  --theme-border-primary: #333333;
  --theme-border-secondary: #2a2a2a;
  --theme-border-focus: #4a9eff;
  
  --theme-accent-primary: #4a9eff;
  --theme-accent-secondary: #9775fa;
  --theme-accent-success: #44ff44;
  --theme-accent-warning: #ffc107;
  --theme-accent-danger: #ff4444;
  --theme-accent-info: #4444ff;
  
  /* Interactive states */
  --theme-hover-bg: rgba(255, 255, 255, 0.08);
  --theme-active-bg: rgba(255, 255, 255, 0.12);
  --theme-focus-bg: rgba(74, 158, 255, 0.2);
  --theme-disabled-bg: #2a2a2a;
  --theme-disabled-text: #888888;
  
  /* Shadows and effects */
  --theme-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
  --theme-shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
  --theme-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.5);
  --theme-overlay: rgba(0, 0, 0, 0.7);
  --theme-backdrop: rgba(15, 15, 15, 0.8);
  
  /* Component updates */
  --theme-tooltip-bg: var(--theme-bg-tertiary);
  --theme-tooltip-text: var(--theme-text-primary);
  --theme-button-bg: #4a4a4a;
  --theme-button-text: var(--theme-text-primary);
}

/* === AUTO THEME (System Preference) === */
[data-theme="auto"] {
  /* Light theme by default */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f8f9fa;
  --theme-text-primary: #212529;
  --theme-text-secondary: #6c757d;
}

@media (prefers-color-scheme: dark) {
  [data-theme="auto"] {
    /* Dark theme when system prefers dark */
    --theme-bg-primary: #0f0f0f;
    --theme-bg-secondary: #1a1a1a;
    --theme-bg-tertiary: #2a2a2a;
    --theme-text-primary: #ffffff;
    --theme-text-secondary: #cccccc;
    --theme-text-tertiary: #888888;
    --theme-border-primary: #333333;
    --theme-accent-primary: #4a9eff;
    --theme-shadow-md: rgba(0, 0, 0, 0.4);
    --theme-overlay: rgba(0, 0, 0, 0.7);
  }
}

/* === THEME TRANSITIONS === */
* {
  transition: 
    background-color var(--theme-transition-fast),
    color var(--theme-transition-fast),
    border-color var(--theme-transition-fast),
    box-shadow var(--theme-transition-fast);
}

/* === REDUCED MOTION SUPPORT === */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
  }
}

/* === HIGH CONTRAST SUPPORT === */
@media (prefers-contrast: high) {
  :root {
    --theme-border-primary: #000000;
    --theme-text-secondary: var(--theme-text-primary);
  }
  
  [data-theme="dark"] {
    --theme-border-primary: #ffffff;
    --theme-text-secondary: var(--theme-text-primary);
  }
}

/* === COMPONENT BASE STYLES === */
body {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  transition: background-color var(--theme-transition-normal), color var(--theme-transition-normal);
}

.theme-card {
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-card-border);
  box-shadow: var(--theme-shadow-sm);
}

.theme-button {
  background-color: var(--theme-button-bg);
  color: var(--theme-button-text);
  border: 1px solid transparent;
}

.theme-button:hover {
  background-color: var(--theme-hover-bg);
}

.theme-input {
  background-color: var(--theme-input-bg);
  border: 1px solid var(--theme-input-border);
  color: var(--theme-text-primary);
}

.theme-input:focus {
  border-color: var(--theme-border-focus);
  box-shadow: 0 0 0 0.2rem var(--theme-focus-bg);
}

/* === UTILITY CLASSES === */
.theme-bg-primary { background-color: var(--theme-bg-primary); }
.theme-bg-secondary { background-color: var(--theme-bg-secondary); }
.theme-text-primary { color: var(--theme-text-primary); }
.theme-text-secondary { color: var(--theme-text-secondary); }
.theme-border { border-color: var(--theme-border-primary); }
.theme-shadow { box-shadow: var(--theme-shadow-md); }
